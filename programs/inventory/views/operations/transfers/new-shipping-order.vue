<template>
    <ui-view
        type="form"
        :model="model"
        :schema="schema"
        :title="$t('New Shipping Order')"
        :before-init="beforeInit"
        :before-validate="beforeValidate"
        :before-submit="beforeSubmit"
        actions="edit"
        :dialog-width="450"
        :dialog-height="590"
        @changed="handleChange"
        v-if="isInitialized"
    >
        <ui-field
            name="carrierId"
            collection="logistics.carriers"
            view="logistics.configuration.carriers"
            :extra-fields="['code']"
            :template="'{{code}} - {{name}}'"
            disable-create
            disable-detail
            label-position="top"
        />
        <ui-field
            name="packageTypeId"
            collection="logistics.package-types"
            view="logistics.configuration.package-types"
            :filters="{carrierId: model.carrierId}"
            disable-create
            disable-detail
            :disabled="!model.carrierId"
            label-position="top"
        />
        <ui-field name="cashOnDeliveryAmount" :precision="$setting('system.currencyPrecision')" label-position="top">
            <div :slot="currencyFormat.currency.symbolPosition === 'after' ? 'append' : 'prepend'">
                {{ currencyFormat.currency.symbol }}
            </div>
        </ui-field>
        <ui-field
            name="shippingPaymentType"
            :options="shippingPaymentTypeOptions"
            translate-labels
            label-position="top"
        />
        <ui-field name="packagingType" :options="packagingTypeOptions" translate-labels label-position="top" />
        <ui-field
            name="weight"
            :precision="$setting('system.unitPrecision')"
            label-position="top"
            :disabled="!model.packageTypeId"
        >
            <div :slot="'append'">kg</div>
        </ui-field>
        <ui-field
            name="volumetricWeight"
            :precision="$setting('system.unitPrecision')"
            label-position="top"
            :disabled="!model.packageTypeId"
        >
            <div :slot="'append'">deci</div>
        </ui-field>

        <ui-field
            name="deliveryAddress"
            label-position="top"
            field-type="compact-address"
            :is-preview="$params('isPreview')"
        />
    </ui-view>
</template>

<script>
export default {
    data: () => ({
        model: {},
        currencyFormat: null,
        shippingPaymentTypeOptions: [
            {value: 'freight-prepaid', label: 'Freight prepaid'},
            {value: 'freight-collect', label: 'Freight collect'}
        ],
        packagingTypeOptions: [
            {value: 'package', label: 'Package'},
            {value: 'parcel', label: 'Parcel'},
            {value: 'file', label: 'File'},
            {value: 'mb', label: 'Manifest bag'}
        ],
        volumetricWeightFactor: 5000,
        isInitialized: false
    }),

    computed: {
        schema() {
            return {
                carrierId: {
                    type: 'string',
                    label: 'Carrier'
                },
                packageTypeId: {
                    type: 'string',
                    label: 'Package type'
                },
                cashOnDeliveryAmount: {
                    type: 'decimal',
                    label: 'Cash on delivery amount',
                    required: false
                },
                shippingPaymentType: {
                    type: 'string',
                    label: 'Shipment payment type',
                    default: 'freight-prepaid'
                },
                packagingType: {
                    type: 'string',
                    label: 'Packaging type',
                    default: 'package'
                },
                weight: {
                    type: 'decimal',
                    label: 'Weight'
                },
                volumetricWeight: {
                    type: 'decimal',
                    label: 'Volumetric weight'
                },
                deliveryAddress: {
                    type: 'object',
                    label: 'Delivery address',
                    blackbox: true,
                    required: false
                }
            };
        }
    },

    methods: {
        async beforeInit(model) {
            const packageType = await this.$collection('logistics.package-types').findOne({
                carrierId: model.carrierId,
                isDefault: true,
                $select: ['_id']
            });
            if (packageType) {
                model.packageTypeId = packageType._id;
                await this.handleChange(model, 'packageTypeId');
            }

            return model;
        },
        async beforeValidate(model) {
            return model;
        },
        async beforeSubmit(model) {
            return model;
        },
        async handleChange(model, field) {
            if (field === 'carrierId' && !!model.carrierId) {
                const carrier = await this.$collection('logistics.carriers').findOne({
                    _id: model.carrierId,
                    $select: ['volumetricWeightFactor'],
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });

                const packageType = await this.$collection('logistics.package-types').findOne({
                    carrierId: model.carrierId,
                    isDefault: true,
                    $select: ['_id']
                });
                if (packageType) {
                    model.packageTypeId = packageType._id;
                } else {
                    model.packageTypeId = '';
                }

                this.volumetricWeightFactor = carrier.volumetricWeightFactor;
            }
            if (field === 'packageTypeId' && !!model.packageTypeId) {
                const packageType = await this.$collection('logistics.package-types').findOne({
                    _id: model.packageTypeId
                });
                const units = await this.$collection('kernel.units').find({
                    _id: {
                        $in: [
                            packageType.grossWeightUnitId,
                            packageType.widthUnitId,
                            packageType.heightUnitId,
                            packageType.depthUnitId
                        ]
                    },
                    $disableSoftDelete: true,
                    $disableActiveCheck: true
                });
                const unitsMap = {};
                for (const unit of units) {
                    unitsMap[unit._id] = unit;
                }
                const round = n => this.$app.roundNumber(n, 4);

                const heightUnitId = packageType.heightUnitId;
                const widthUnitId = packageType.widthUnitId;
                const depthUnitId = packageType.depthUnitId;
                const grossWeightUnitId = packageType.defaultWeightUnitId;

                let width = packageType.width;
                let height = packageType.height;
                let depth = packageType.depth;
                let weight = packageType.defaultWeight;

                const widthUnit = unitsMap[widthUnitId];
                const heightUnit = unitsMap[heightUnitId];
                const depthUnit = unitsMap[depthUnitId];
                const grossWeightUnit = unitsMap[grossWeightUnitId];

                if (widthUnit) {
                    if (widthUnit.type === 'smaller') width = round(round(width) / round(widthUnit.ratio));
                    else if (widthUnit.type === 'reference') width = round(width);
                    else if (widthUnit.type === 'bigger') width = round(width) * round(widthUnit.ratio);
                }
                if (heightUnit) {
                    if (heightUnit.type === 'smaller') height = round(round(height) / round(heightUnit.ratio));
                    else if (heightUnit.type === 'reference') height = round(height);
                    else if (heightUnit.type === 'bigger') height = round(height) * round(heightUnit.ratio);
                }
                if (depthUnit) {
                    if (depthUnit.type === 'smaller') depth = round(round(depth) / round(depthUnit.ratio));
                    else if (depthUnit.type === 'reference') depth = round(depth);
                    else if (depthUnit.type === 'bigger') depth = round(depth) * round(depthUnit.ratio);
                }
                if (grossWeightUnit) {
                    if (grossWeightUnit.type === 'smaller')
                        weight = round(round(weight) / round(grossWeightUnit.ratio));
                    else if (grossWeightUnit.type === 'reference') weight = round(weight);
                    else if (grossWeightUnit.type === 'bigger') weight = round(weight) * round(grossWeightUnit.ratio);
                }

                width = round(width * 100);
                height = round(height * 100);
                depth = round(depth * 100);

                const volumetricWeight = round((width * height * depth) / (this.volumetricWeightFactor || 5000));

                this.model.weight = weight;
                this.model.volumetricWeight = volumetricWeight;
                model.weight = weight;
                model.volumetricWeight = volumetricWeight;
            }
        }
    },

    async created() {
        this.$params('loading', true);

        const company = this.$store.getters['session/company'];
        this.currencyFormat = {
            currency: {
                symbol: company.currency.symbol,
                format: company.currency.symbolPosition === 'before' ? '%s %v' : '%v %s',
                symbolPosition: company.currency.symbolPosition
            }
        };

        this.isInitialized = true;

        this.$params('loading', false);
    }
};
</script>
