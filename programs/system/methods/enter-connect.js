import notepack from 'notepack.io';
import microtime from 'microtime';

export default [
    {
        name: 'enter-connect-get-printers',
        async action() {
            const app = this.app;
            const operationId = 'POID-' + microtime.now();

            await app.cache.client.publish(
                'real-time-events',
                notepack.encode({
                    event: 'enter-connect:get-printers',
                    data: {
                        operationId
                    }
                })
            );

            const startTime = Date.now();
            return await new Promise(resolve => {
                const checkIdx = setInterval(async () => {
                    const elapsed = Date.now() - startTime;

                    const result = await app.db
                        .collection('system_enter-connect-printers')
                        .find({
                            operationId
                        })
                        .toArray();

                    if (result.length > 0 && elapsed >= 250) {
                        resolve(
                            result.map(printer => ({
                                ...printer,
                                _id: printer._id.toString()
                            }))
                        );

                        clearInterval(checkIdx);
                    } else if (elapsed > 5000) {
                        clearInterval(checkIdx);

                        resolve([]);
                    }
                }, 250);
            });
        }
    }
];
