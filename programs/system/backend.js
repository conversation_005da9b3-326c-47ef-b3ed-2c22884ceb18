import initEnterConnect from './init/enter-connect';
import initUpdateLicense from './init/update-license';

export default async function (app) {
    await initEnterConnect(app);
    initUpdateLicense(app);

    // setTimeout(async () => {
    //     console.log('Getting printers---');
    //     const result = await app.rpc('system.enter-connect-get-printers');
    //     console.log(result);
    // }, 2000);
}

// const files = await app.collection('kernel.files').find({});
// const operations = [];
// for (const file of files) {
//     operations.push({
//         updateOne: {
//             filter: {_id: file._id},
//             update: {
//                 $set: {
//                     path: file.path.replace('/opt/entererp-files', '/Users/<USER>/Projects/entererp/storage/files')
//                 }
//             }
//         }
//     });
// }
//
// await app.collection('kernel.files').bulkWrite(operations);
// console.log('done');
