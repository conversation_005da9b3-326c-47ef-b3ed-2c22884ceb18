import notepack from 'notepack.io';

export default async function (app) {
    app.on('socketio-initialized', io => {
        io.on('connection', async socket => {
            const {user, macAddress} = await initSession(app, socket);
            if (!user) return;

            const removeChatListeners = await initEnterConnect(app, socket, user, macAddress);

            socket.on('disconnect', () => {
                removeChatListeners();
            });
        });
    });
}

async function initSession(app, socket) {
    const query = socket.handshake.query ?? {};

    const userId = query.userId;
    const macAddress = query.macAddress;
    const isEnterConnectClient = query.isEnterConnectClient === 'yes';
    if (!userId || !macAddress || !isEnterConnectClient) return {user: null, macAddress: null};

    const user = await app.collection('kernel.users').findOne({_id: userId, $select: ['_id', 'name']});
    if (!user) {
        return {user: null, macAddress: null};
    }

    return {user, macAddress};
}

async function initEnterConnect(app, socket, user, macAddress) {
    const sub = app.realTimeEventSubscription;

    const onMessage = (channel, buffer) => {
        if (channel.toString() !== 'real-time-events') {
            return;
        }

        const payload = notepack.decode(buffer);

        if (!payload || !payload.data) {
            return;
        }

        const event = payload.event;
        const data = payload.data;

        if (event === 'enter-connect:get-printers' && typeof data.operationId === 'string' && data.operationId !== '') {
            socket.emit('enter-connect:get-printers', {
                operationId: data.operationId
            });
        }
    };

    socket.on('enter-connect:sync-printers', async payload => {
        const {operationId, printers} = payload;

        if (printers.length > 0) {
            await app.db.collection('system_enter-connect-printers').insertMany(
                printers.map(printer => ({
                    ...printer,
                    operationId,
                    createdAt: app.datetime.local().toJSDate()
                }))
            );
        }
    });

    sub.on('messageBuffer', onMessage);

    return () => {
        sub.removeListener('messageBuffer', onMessage);
    };
}
