import _ from 'lodash';
import {PazaramaClient} from './utils';

export default async function (app, store) {
    const client = await PazaramaClient(app, store.integrationParams);

    const cargoCompaniesResult = (await client.get('/sellerRegister/getSellerDelivery')).data;

    console.log('cargoCompaniesResult', cargoCompaniesResult);
    
    const cargoCompanies = _.get(cargoCompaniesResult, 'data.cargoCompany.cargoCompanies', []);
    
    console.log('cargoCompanies', cargoCompanies);
    return _.uniqBy(
        cargoCompanies.map(cargoCompany => ({
            id: cargoCompany.cargoCompanyId,
            name: cargoCompany.cargoCompanyName
        })),
        'id'
    );
}
