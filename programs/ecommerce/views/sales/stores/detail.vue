<template>
    <ui-view
        type="content"
        :title="title"
        collection="ecommerce.stores"
        class="ecommerce-sales-stores-detail"
        :top-panel-height="60"
        progress-id="ecommerce.sales.stores.detail"
        :extra-actions="extraActions"
        v-if="!!initialized"
    >
        <template slot="top-panel">
            <div class="store-toolbar">
                <div class="store-logo">
                    <img :src="storeLogo" />
                </div>

                <div class="store-details">
                    <div class="store-name">{{ store.name }}</div>

                    <div class="store-description">{{ store.code }} - {{ integrationType.title }}</div>
                </div>

                <div class="top-reports">
                    <div class="top-report">
                        <div class="report-label">{{ 'Published Products' | t }}</div>
                        <div class="report-value">{{ store.publishedProductCount }}</div>
                    </div>

                    <div class="top-report">
                        <div class="report-label">{{ 'Un-Published Products' | t }}</div>
                        <div class="report-value">{{ store.unPublishedProductCount }}</div>
                    </div>

                    <div class="top-report">
                        <div class="report-label">{{ 'Total Products' | t }}</div>
                        <div class="report-value">{{ store.totalProductCount }}</div>
                    </div>
                </div>
            </div>
        </template>

        <el-tabs v-model="activeTab" class="full-tabs">
            <el-tab-pane name="products" :label="'PRODUCTS' | t">
                <tab-products
                    :store="store"
                    :integration-type="integrationType"
                    :format-options="formatOptions"
                    v-if="activeTab === 'products'"
                />
            </el-tab-pane>

            <el-tab-pane name="reviews" :label="'REVIEWS' | t" :disabled="integrationType.name !== 'enterstore'">
                <tab-reviews
                    :store="store"
                    :integration-type="integrationType"
                    :format-options="formatOptions"
                    v-if="activeTab === 'reviews'"
                />
            </el-tab-pane>

            <el-tab-pane name="transaction-history" :label="'TRANSACTIONS' | t" v-if="isTransactionsShown">
                <tab-transaction-history
                    :store="store"
                    :integration-type="integrationType"
                    :format-options="formatOptions"
                    v-if="activeTab === 'transaction-history'"
                />
            </el-tab-pane>
        </el-tabs>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import {trim} from 'framework/helpers';
import integrationTypes from '../../../integrations/integration-types';
import TabProducts from './detail/_tab-products';
import TabReviews from './detail/_tab-reviews';
import TabTransactionHistory from './detail/_tab-transaction-history';

export default {
    data: () => ({
        store: null,
        formatOptions: null,
        activeTab: 'products',
        initialized: false
    }),

    computed: {
        title() {
            return this.store.name || '';
        },
        integrationType() {
            return integrationTypes.find(it => it.name === this.store.integrationType);
        },
        storeLogo() {
            return this.$app.absoluteUrl(trim(this.integrationType.logo, '/'));
        },
        extraActions() {
            const hasTransactionStatuses = [
                'trendyol',
                'hepsiburada',
                'pazarama',
                'ciceksepeti',
                'koctas',
                'n11'
            ].includes(this.store.integrationType);
            const hasOrderDownload = [
                'shopify',
                'trendyol',
                'hepsiburada',
                'pazarama',
                'pttavm',
                'n11',
                'ciceksepeti',
                'koctas'
            ].includes(this.store.integrationType);
            const hasProductDownload = [
                'shopify',
                'trendyol',
                'hepsiburada',
                'pazarama',
                'pttavm',
                'n11',
                'ciceksepeti',
                'koctas'
            ].includes(this.store.integrationType);

            return [
                ...(hasTransactionStatuses
                    ? [
                          {
                              name: 'check-transaction-statuses',
                              title: 'Check Transaction Statuses',
                              icon: 'fal fa-sync',
                              handler: this.handleCheckTransactionStatuses
                          }
                      ]
                    : []),
                ...(hasOrderDownload
                    ? [
                          {
                              name: 'import-orders',
                              title: 'Import Orders',
                              icon: 'fal fa-cloud-download',
                              handler: this.handleImportOrders
                          },
                          {
                              name: 'inquire',
                              title: 'Import Orders (By Date)',
                              icon: 'fal fa-cloud-download',
                              handler: this.handleInquire
                          }
                      ]
                    : []),
                ...(hasProductDownload
                    ? [
                          {
                              name: 'import-products',
                              title: 'Import Products',
                              icon: 'fal fa-cloud-download',
                              handler: this.handleImportProducts
                          }
                      ]
                    : []),
                {
                    name: 'product-xmls',
                    title: 'Products XML Data',
                    icon: 'fal fa-link',
                    hidden: () => {
                        return this.store.integrationType !== 'enterstore';
                    },
                    handler: () => {
                        this.$program.dialog({
                            component: 'ecommerce.sales.stores.detail.xmls',
                            params: {
                                id: this.$params('id'),
                                store: this.store
                            }
                        });
                    }
                },
                {
                    name: 'product-mappings',
                    title: 'Manual Product Mappings',
                    icon: 'fal fa-link',
                    hidden: () => {
                        return this.store.integrationType === 'enterstore' || this.store.integrationType === 'shopify';
                    },
                    handler: () => {
                        this.$program.dialog({
                            component: 'ecommerce.sales.stores.detail.product-mappings',
                            params: {
                                id: this.$params('id'),
                                store: this.store,
                                forcedPreview: false
                            }
                        });
                    }
                },
                {
                    name: 'critical-stock-import',
                    title: 'Critical Stock Import',
                    icon: 'fal fa-cloud-upload',
                    handler: () => {
                        this.$program.dialog({
                            component: 'ecommerce.sales.stores.detail.critical-stock-import',
                            params: {
                                id: this.$params('id'),
                                store: this.store,
                                forcedPreview: false
                            }
                        });
                    },
                    hidden: () => {
                        return this.store.integrationType === 'enterstore';
                    }
                }
            ];
        },
        isTransactionsShown() {
            const integrationType = (this.integrationType || {}).name;
            const excludedTypes = ['enterstore', 'shopify'];

            return !excludedTypes.includes(integrationType);
        }
    },

    methods: {
        async handleCheckTransactionStatuses() {
            this.$params('loading', true);

            try {
                await this.$rpc('ecommerce.stores-check-transaction-statuses', {
                    storeId: this.$params('id')
                });
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        async handleImportOrders() {
            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t('Your pending orders in the integration system will be imported. Do you want to continue?'),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });

            if (!isConfirmed) return;

            this.$params('loading', true);

            try {
                await this.$rpc('ecommerce.stores-import-orders', {
                    storeId: this.$params('id')
                });
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        async handleInquire() {
            this.$program.dialog({
                component: 'ecommerce.sales.stores.detail.inquire',
                params: {
                    title: this.$t('Inquire')
                },
                onSubmit: async ({startDate, endDate}) => {
                    this.$params('loading', true);

                    try {
                        await this.$rpc('ecommerce.stores-import-orders', {
                            storeId: this.$params('id'),
                            startDate,
                            endDate
                        });
                    } catch (error) {
                        this.$program.message('error', error.message);
                    }
                    this.$nextTick(() => {
                        this.$params('loading', false);
                    });
                }
            });
        },
        async handleImportProducts() {
            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t(
                        'Your products in the integration will be matched with your products in the ERP. System will try to perform the matching process through product codes or product barcodes. Do you want to continue?'
                    ),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });

            if (!isConfirmed) return;

            this.$params('loading', true);

            try {
                await this.$rpc('ecommerce.stores-import-products', {
                    storeId: this.$params('id')
                });
            } catch (error) {
                this.$program.message('error', error.message);
            }

            if (!!this.$refs.table) {
                const api = this.$refs.table.api;
                api.forEachNode(node => {
                    if (this.selected.indexOf(node.data._id) !== -1) {
                        node.setSelected(false);
                    }
                });
            }
            this.selected = [];

            this.$params('loading', false);
        },

        async initStore() {
            this.store = await this.$collection('ecommerce.stores').findOne({
                _id: this.$params('id'),
                $select: [
                    '_id',
                    'code',
                    'name',
                    'currencyId',
                    'pricingPolicy',
                    'discountedPriceListId',
                    'stockPolicy',
                    'deliveryOptions',
                    'integrationType',
                    'publishedProductCount',
                    'unPublishedProductCount',
                    'totalProductCount',
                    'barcodeMatchingOnly'
                ]
            });
            const currency = await this.$collection('kernel.currencies').findOne({
                _id: this.store.currencyId
            });
            this.formatOptions = _.cloneDeep(this.$store.getters['session/formatterOptions']);
            this.formatOptions.currency = {
                symbol: currency.symbol,
                symbolPosition: currency.symbolPosition,
                format: currency.symbolPosition === 'before' ? '%s %v' : '%v %s'
            };
        }
    },

    async created() {
        this.initStoreDevounced = _.debounce(this.initStore, 250, {leading: false, trailing: true});

        this.$params('loading', true);

        await this.initStore();

        this.$collection('ecommerce.stores').on('all', this.initStoreDevounced);

        this.initialized = true;

        this.$params('loading', false);
    },

    beforeDestroy() {
        this.$collection('ecommerce.stores').removeListener('all', this.initStoreDevounced);
    },

    components: {
        TabProducts,
        TabReviews,
        TabTransactionHistory
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.ecommerce-sales-stores-detail {
    background-color: #fff;

    .store-toolbar {
        display: flex;
        flex-flow: row nowrap;

        .store-logo {
            flex: 0 0 40px;
            height: 40px;

            img {
                display: block;
                width: 100%;
                height: 100%;
                border-radius: $border-radius-large;
            }
        }

        .store-details {
            margin-left: 15px;
            line-height: 1;

            .store-name {
                margin-bottom: 9px;
                font-size: 18px;
                font-weight: $font-weight-bold;
            }

            .store-description {
                font-size: 13px;
            }
        }

        .top-reports {
            display: flex;
            flex: 1 1 0;
            justify-content: flex-end;
            margin-right: 6.5px;

            .top-report {
                padding: 0 25px;
                border-right: 1px solid $border-color-light;
                line-height: 1;

                &:first-child {
                    padding-left: 0;
                }

                &:last-child {
                    padding-right: 0;
                    border-right: none;
                }

                .report-label {
                    margin-bottom: 6px;
                    font-size: 12px;
                    color: $text-color-lighter;
                }

                .report-value {
                    font-size: 21px;
                }
            }
        }
    }
}
</style>
