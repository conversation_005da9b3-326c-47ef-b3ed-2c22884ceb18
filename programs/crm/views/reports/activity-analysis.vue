<template>
    <ui-view
        type="content"
        :title="'Activity Analysis' | t"
        class="crm-reports-activity-analysis"
        :class="{'has-graph': hasGraph}"
        v-if="initialized"
    >
        <template slot="top-panel">
            <ui-scope
                ref="scope"
                id="crm.reports.activity-analysis"
                :view-modes="viewModes"
                :default-view-mode="!!$params('showDocumentActivities') ? 'table' : 'graph'"
                :filters="scopeApplicableFilters"
                @changed="handleScopeChange"
            />
        </template>

        <div class="report-container">
            <div class="report-graph" v-if="hasGraph">
                <div class="type-report">
                    <ui-chartx :options="typeChartOptions" />
                </div>
                <div class="document-type-report">
                    <ui-chartx :options="documentTypeChartOptions" />
                </div>
                <div class="status-report">
                    <ui-chartx :options="statusChartOptions" />
                </div>
            </div>
            <div class="report-table">
                <ui-table
                    ref="table"
                    id="crm.reports.activity-analysis"
                    collection="kernel.activities"
                    :columns="columns"
                    :filters="filters"
                    :extra-fields="extraFields"
                    :process-result="processResult"
                    :summary-row="summaryRow"
                    :enable-selection="false"
                />
            </div>
        </div>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import {rawMongoQuery} from 'framework/helpers';

const activityTypes = [
    {value: 'task', label: 'Task', color: 'blue', icon: 'tasks', view: 'crm.activities.tasks-detail'},
    {
        value: 'meeting',
        label: 'Meeting',
        color: 'green',
        icon: 'presentation',
        view: 'crm.activities.meetings-detail'
    },
    {
        value: 'appointment',
        label: 'Appointment',
        color: 'orange',
        icon: 'tags',
        view: 'crm.activities.appointments-detail'
    },
    {
        value: 'phone-call',
        label: 'Phone Call',
        color: 'purple',
        icon: 'phone-volume',
        view: 'crm.activities.phone-calls-detail'
    },
    {value: 'event', label: 'Event', color: 'pink', icon: 'calendar-alt', view: 'crm.activities.event-detail'}
];
const documentTypes = [
    {value: 'crm.leads', label: 'Lead', view: 'crm.customer-relations.leads'},
    {value: 'crm.opportunities', label: 'Opportunity', view: 'crm.customer-relations.opportunities'},
    {value: 'sale.quotations', label: 'Quotation', view: 'crm.customer-relations.quotations'},
    {value: 'sale.orders', label: 'Order', view: 'sale.sales.orders'},
    {value: 'accounting.customer-invoices', label: 'Invoice', view: 'accounting.sales.customer-invoices'},
    {value: 'kernel.partners', label: 'Partner', view: 'crm.customer-relations.customers'},
    {value: 'kernel.activities', label: 'Activity', view: 'calendar.activities.detail'}
];
const statuses = [
    {value: 'open', label: 'Open'},
    {value: 'stopped', label: 'Stopped'},
    {value: 'started', label: 'Started'},
    {value: 'canceled', label: 'Canceled'},
    {value: 'closed', label: 'Closed'}
];
const pieChartOptions = {
    chart: {
        type: 'pie',
        plotBackgroundColor: null,
        plotBorderWidth: null,
        plotShadow: false,
        spacing: [15, 15, 15, 15]
    },
    tooltip: {
        pointFormat: '{point.y}: <b>{point.percentage:.2f}%</b>'
    },
    legend: {
        layout: 'vertical',
        align: 'left',
        verticalAlign: 'bottom',
        itemMarginTop: 5,
        itemMarginBottom: 5
    },
    accessibility: {
        point: {
            valueSuffix: '%'
        }
    },
    plotOptions: {
        pie: {
            allowPointSelect: true,
            cursor: 'pointer',
            dataLabels: {
                enabled: false
            },
            showInLegend: true
        }
    }
};

export default {
    data: () => ({
        viewMode: 'graph',
        scopeQuery: {},
        report: [],
        extraFields: [
            'partnerId',
            'leadId',
            'linkedDocumentId',
            'linkedDocumentType',
            'assignedToType',
            'params.assignedTo'
        ],
        rowCount: 0,
        initialized: false
    }),

    computed: {
        hasGraph() {
            if (!!this.$params('showDocumentActivities')) {
                return false;
            }

            return this.viewMode === 'graph';
        },
        viewModes() {
            return !!this.$params('showDocumentActivities') ? ['table'] : ['graph', 'table'];
        },
        typeChartOptions() {
            const self = this;
            const grouped = _.groupBy(this.report, 'type');
            const data = [];

            for (const key of Object.keys(grouped)) {
                data.push({
                    name: key,
                    y: _.sumBy(grouped[key], 'count')
                });
            }

            return {
                ...pieChartOptions,
                title: {
                    text: this.$t('Activity Types')
                },
                plotOptions: {
                    ...pieChartOptions.plotOptions,
                    series: {
                        cursor: 'pointer',
                        point: {
                            events: {
                                click() {
                                    self.handlePieClick('type', this.name);
                                }
                            }
                        }
                    }
                },
                series: [
                    {
                        colorByPoint: true,
                        data
                    }
                ]
            };
        },
        documentTypeChartOptions() {
            const self = this;
            const grouped = _.groupBy(this.report, 'documentType');
            const data = [];

            for (const key of Object.keys(grouped)) {
                data.push({
                    name: key,
                    y: _.sumBy(grouped[key], 'count')
                });
            }

            return {
                ...pieChartOptions,
                title: {
                    text: this.$t('Document Types')
                },
                plotOptions: {
                    ...pieChartOptions.plotOptions,
                    series: {
                        cursor: 'pointer',
                        point: {
                            events: {
                                click() {
                                    self.handlePieClick('documentType', this.name);
                                }
                            }
                        }
                    }
                },
                series: [
                    {
                        colorByPoint: true,
                        data
                    }
                ]
            };
        },
        statusChartOptions() {
            const self = this;
            const grouped = _.groupBy(this.report, 'status');
            const data = [];

            for (const key of Object.keys(grouped)) {
                data.push({
                    name: key,
                    y: _.sumBy(grouped[key], 'count')
                });
            }

            return {
                ...pieChartOptions,
                title: {
                    text: this.$t('Statuses')
                },
                plotOptions: {
                    ...pieChartOptions.plotOptions,
                    series: {
                        cursor: 'pointer',
                        point: {
                            events: {
                                click() {
                                    self.handlePieClick('status', this.name);
                                }
                            }
                        }
                    }
                },
                series: [
                    {
                        colorByPoint: true,
                        data
                    }
                ]
            };
        },
        filters() {
            const filters = {
                ...this.scopeQuery,
                ...(this.$params('filters') || {})
            };

            if (!Array.isArray(filters.$or)) {
                filters.$or = [];
            }

            filters.$or.push({partnerType: {$exists: false}});
            filters.$or.push({partnerType: null});
            filters.$or.push({partnerType: ''});
            filters.$or.push({partnerType: {$in: ['customer', 'lead']}});

            // Get linked document type.
            let linkedDocumentType = null;
            if (_.isObject(filters.linkedDocumentType) && Array.isArray(filters.linkedDocumentType.$in)) {
                linkedDocumentType = filters.linkedDocumentType.$in;

                delete filters.linkedDocumentType;
            } else if (Array.isArray(filters.$and)) {
                filters.$and.forEach(q => {
                    if (_.isObject(q.linkedDocumentType) && Array.isArray(q.linkedDocumentType.$in)) {
                        linkedDocumentType = q.linkedDocumentType.$in;
                    }
                });

                filters.$and = filters.$and.filter(q => !_.isObject(q.linkedDocumentType));
            }
            if (!_.isNull(linkedDocumentType)) {
                filters.linkedDocumentType = {$in: linkedDocumentType};
            }

            // Fix filters.
            if (Array.isArray(filters.$and) && filters.$and.length === 0) {
                delete filters.$and;
            }

            return filters;
        },
        columns() {
            return [
                {
                    field: 'lead.name',
                    label: 'Lead',
                    hidden: true
                },
                {
                    field: 'partner.name',
                    label: 'Customer',
                    hidden: true
                },
                {
                    field: 'status',
                    label: 'Status',
                    translateLabels: true,
                    valueLabels: statuses,
                    width: 120
                },
                {
                    field: 'code',
                    label: 'Code',
                    width: 150,
                    relationParams(params) {
                        const data = params.data;

                        if (_.isPlainObject(data) && !!data.type) {
                            return {
                                id: data._id,
                                customLabel: data.code,
                                view: activityTypes.find(t => t.value === data.type).view
                            };
                        }

                        return {};
                    }
                },
                {
                    field: 'type',
                    label: 'Activity type',
                    tagsCell: true,
                    tagLabels: activityTypes,
                    translateLabels: true,
                    width: 120
                },
                {
                    field: 'duration',
                    label: 'Duration',
                    width: 120
                },
                {
                    field: 'linkedDocumentType',
                    label: 'Document type',
                    valueLabels: documentTypes,
                    translateLabels: true,
                    width: 120
                },
                {
                    field: 'linkedDocumentCode',
                    label: 'Document code',
                    relationParams(params) {
                        const data = params.data;

                        console.log('data',data)

                        if (_.isPlainObject(data) && !!data.linkedDocumentType && !!data.linkedDocumentCode) {
                            return {
                                id: data.linkedDocumentId,
                                customLabel: data.linkedDocumentCode,
                                view: (documentTypes.find(dt => dt.value === data.linkedDocumentType) || {}).view
                            };
                        }

                        return {};
                    },
                    width: 150
                },
                {
                    field: 'customer-or-lead',
                    label: 'Customer / Lead',
                    relationParams(params) {
                        const data = params.data;

                        if (_.isPlainObject(data)) {
                            if (_.isPlainObject(data.partner)) {
                                return {
                                    view: 'crm.customer-relations.customers-detail',
                                    id: data.partnerId,
                                    customLabel: data.partner.name
                                };
                            } else if (_.isPlainObject(data.lead)) {
                                return {
                                    view: 'crm.customer-relations.leads-detail',
                                    id: data.leadId,
                                    customLabel: data.lead.name
                                };
                            }
                        }
                    }
                },
                {
                    field: 'startDate',
                    label: 'Start date',
                    sort: 'desc',
                    format: 'datetime',
                    width: 150
                },
                {
                    field: 'endDate',
                    label: 'End date',
                    format: 'datetime',
                    width: 150
                },
                {
                    field: 'subject',
                    label: 'Subject'
                },
                {
                    field: 'assignedBy',
                    label: 'Assigned by',
                    subSelect: ['code', 'name'],
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};

                        relation.isVisible =
                            _.isObject(data.assignedBy) &&
                            _.isString(data.assignedBy.code) &&
                            _.isString(data.assignedBy.name);

                        if (relation.isVisible) {
                            relation.view = 'partners.partners-detail';
                            relation.id = data.assignedById;
                            relation.template = '{{code}} - {{name}}';
                        }

                        return relation;
                    },
                    visible: false,
                    width: 180
                },
                {
                    field: 'assignedTo',
                    label: 'Assigned to',
                    relationParams(params) {
                        const data = params.data;
                        const {assignedTo} = data.params || {};
                        const relation = {};

                        relation.isVisible =
                            _.isObject(assignedTo) && _.isString(assignedTo.code) && _.isString(assignedTo.name);

                        if (relation.isVisible) {
                            if (data.assignedToType === 'employee') {
                                relation.view = 'partners.partners-detail';
                                relation.id = assignedTo.id;
                                relation.customLabel = `${assignedTo.code} - ${assignedTo.name}`;
                            } else {
                                relation.view = 'system.members.users-detail';
                                relation.id = assignedTo.id;
                                relation.customLabel = `${assignedTo.code} - ${assignedTo.name}`;
                            }
                        }

                        return relation;
                    },
                    width: 180
                },
                {
                    field: 'priority',
                    label: 'Priority',
                    translateLabels: true,
                    valueLabels: [
                        {value: 'not-urgent', label: 'Not urgent'},
                        {value: 'normal', label: 'Normal'},
                        {value: 'urgent', label: 'Urgent'},
                        {value: 'very-urgent', label: 'Very urgent'}
                    ],
                    width: 120
                }
            ];
        },
        scopeApplicableFilters() {
            const self = this;

            return [
                // Pre-defined
                {code: 'today', label: 'Today', query: 'startDate|today'},
                {code: 'thisWeek', label: 'This week', query: 'startDate|thisWeek'},
                {code: 'lastWeek', label: 'Last week', query: 'startDate|lastWeek'},
                {code: 'thisMonth', label: 'This month', query: 'startDate|thisMonth'},
                {code: 'lastMonth', label: 'Last month', query: 'startDate|lastMonth'},

                // Filters.
                {field: 'code', label: 'Code'},
                {
                    field: 'startDate',
                    code: 'startDate',
                    label: 'Start date',
                    type: 'datetime'
                },
                {
                    field: 'endDate',
                    code: 'endDate',
                    label: 'End date',
                    type: 'datetime'
                },
                {
                    field: 'phoneCallStartTime',
                    code: 'phoneCallStartTime',
                    label: 'Phone call start time',
                    type: 'datetime'
                },
                {
                    code: 'type',
                    field: 'type',
                    label: 'Type',
                    translateLabels: true,
                    valueLabels: activityTypes
                },
                {
                    code: 'linkedDocumentType',
                    field: 'linkedDocumentType',
                    label: 'Document type',
                    translateLabels: true,
                    valueLabels: documentTypes
                },
                {
                    field: 'partnerId',
                    label: 'Partner',
                    collection: 'kernel.partners',
                    extraFields: ['code'],
                    filters: {$sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'leadId',
                    label: 'Lead',
                    collection: 'crm.leads',
                    extraFields: ['code'],
                    filters: {$sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'phoneCallType',
                    label: 'Phone call type',
                    translateLabels: true,
                    items: [
                        {value: 'completed', label: 'Completed phone call'},
                        {value: 'planned', label: 'Planned phone call'}
                    ]
                },
                {
                    field: 'phoneCallDirection',
                    label: 'Phone call direction',
                    translateLabels: true,
                    items: [
                        {value: 'outbound', label: 'Outbound phone call'},
                        {value: 'inbound', label: 'Inbound phone call'}
                    ]
                },
                {
                    field: 'phoneCallDurationMinutes',
                    label: 'Phone call duration (Minute)',
                    type: 'integer'
                },
                {
                    field: 'phoneCallResult',
                    label: 'Phone call result'
                },
                {
                    field: 'meetingLocationId',
                    label: 'Meeting location',
                    collection: 'kernel.meeting-locations',
                    extraFields: ['code'],
                    filters: {$sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'assignedById',
                    label: 'Assigned by',
                    collection: 'kernel.users',
                    extraFields: ['code'],
                    filters: {$sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'params.assignedTo.id',
                    code: 'assignedToEmployeeId',
                    label: 'Assigned to employee',
                    collection: 'kernel.partners',
                    extraFields: ['code'],
                    filters: {type: 'employee', $sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'params.assignedTo.id',
                    code: 'assignedToUserId',
                    label: 'Assigned to user',
                    collection: 'kernel.users',
                    extraFields: ['code'],
                    filters: {$sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    code: 'status',
                    field: 'status',
                    label: 'Status',
                    translateLabels: true,
                    valueLabels: statuses
                }
            ];
        }
    },

    methods: {
        handleScopeChange(model) {
            this.viewMode = model.viewMode;
            this.scopeQuery = model.query;
            this.getReport();
        },
        handlePieClick(chart, data) {
            if (chart === 'type') {
                const type = activityTypes.find(t => this.$t(t.label) === data);

                this.$refs.scope.apply({
                    type: 'filter',
                    code: 'type',
                    value: [type.value]
                });
            } else if (chart === 'documentType') {
                const type = documentTypes.find(t => this.$t(t.label) === data);

                this.$refs.scope.apply({
                    type: 'filter',
                    code: 'linkedDocumentType',
                    value: [type.value]
                });
            } else if (chart === 'status') {
                const status = statuses.find(s => this.$t(s.label) === data);

                this.$refs.scope.apply({
                    type: 'filter',
                    code: 'status',
                    value: [status.value]
                });
            }
        },
        async processResult(result) {
            const documentIds = result.data.filter(d => !!d.linkedDocumentId).map(d => d.linkedDocumentId);
            const documentCodes = {};

            for (const documentType of documentTypes) {
                const collection = this.$collection(documentType.value);

                const documents = await collection.find({
                    _id: {$in: documentIds},
                    $select: ['_id', 'code'],
                    $disableActiveCheck: true,
                    $disableBranchCheck: true,
                    $disableSoftDelete: true
                });

                for (const document of documents) {
                    documentCodes[document._id] = document.code;
                }
            }

            result.data = result.data.map(row => {
                if (_.isDate(row.startDate) && _.isDate(row.endDate)) {
                    const sd = this.$datetime.fromJSDate(row.startDate);
                    const ed = this.$datetime.fromJSDate(row.endDate);
                    row.duration = `${this.$app.roundNumber(ed.diff(sd).as('minutes'))} ${this.$t('Minutes(s)')}`;

                    if (!!row.linkedDocumentId) {
                        row.linkedDocumentCode = documentCodes[row.linkedDocumentId];
                    }
                }

                return row;
            });

            this.rowCount = result.total;

            return result;
        },
        async summaryRow() {
            return {
                rowCount: this.rowCount
            };
        },
        async getReport() {
            const report = await this.$rpc('crm.get-activity-distribution-report', {
                type: this.type,
                query: rawMongoQuery(_.cloneDeep(this.filters))
            });
            const items = [];

            for (const r of report) {
                const item = {};

                item.type = this.$t(activityTypes.find(t => t.value === r.type).label);
                item.documentType = this.$t(documentTypes.find(t => t.value === r.documentType).label);
                item.status = this.$t(statuses.find(s => s.value === r.status).label);
                item.count = r.count || 0;
                
                items.push(item);
            }
            this.report = items;
        }
    },

    async created() {
        this.getReportDebounced = _.debounce(this.getReport, 300, {leading: false, trailing: true});
        this.$collection('kernel.activities').on('all', this.getReportDebounced);

        this.initialized = true;
    },

    beforeDestroy() {
        this.$collection('kernel.activities').removeListener('all', this.getReportDebounced);
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.crm-reports-activity-analysis {
    .report-container {
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    .report-graph {
        position: absolute;
        display: flex;
        flex-flow: row nowrap;
        top: 0;
        left: 0;
        width: 100%;
        height: 220px;
        overflow: hidden;

        .type-report,
        .document-type-report,
        .status-report {
            flex: 0 0 33.33%;
            overflow: hidden;
        }
    }

    .report-table {
        width: 100%;
        height: 100%;
        overflow: hidden;
    }

    &.has-graph {
        .report-container {
            padding-top: 220px;
        }
    }
}
</style>
