import _ from 'lodash';
import TargetTimeCellRenderer from '../../customer-relations/leads/_target-time-cell-renderer';

export default function (vm, type) {
    const targetTimes = vm.$setting('crm.targetTimes');

    if (type === 'lead') {
        const targetTime = targetTimes.find(tt => tt.document === 'lead');

        return [
            {
                field: 'target-time',
                label: 'Duration',
                cellRendererParams: {targetTime},
                cellRendererFramework: TargetTimeCellRenderer,
                cellClass: 'pl0 pr0',
                cellStyle: {border: 'none'},
                width: 90,
                hidden: !targetTime.isActive
            },
            {
                field: 'code',
                label: 'Code',
                relationParams(params) {
                    const data = params.data;
                    const relation = {};
                    console.log('data',data)
                    relation.isVisible = _.isString(data._id) && _.isString(data.code);

                    if (relation.isVisible) {
                        relation.view = 'crm.customer-relations.leads-detail';
                        relation.id = data._id;
                    }

                    return relation;
                },
                width: 150
            },
            {field: 'name', label: 'Name', width: 150},
            {
                field: 'email',
                label: 'Email Address',
                width: 180,
                visible: false
            },
            {
                field: 'branch.name',
                label: 'Branch Office',
                hidden: !vm.$setting('system.multiBranch'),
                width: 180,
                visible: false
            },
            {
                field: 'qualificationLevel.name',
                label: 'Qualification level',
                visible: false
            },
            {field: 'source.name', label: 'Source'},
            {field: 'category.name', label: 'Category', visible: false},
            {
                field: 'communicationChannel.name',
                label: 'Communication channel'
            },
            {
                field: 'priority',
                label: 'Priority',
                translateLabels: true,
                valueLabels: [
                    {value: 'not-urgent', label: 'Not urgent'},
                    {value: 'normal', label: 'Normal'},
                    {value: 'urgent', label: 'Urgent'},
                    {value: 'very-urgent', label: 'Very urgent'}
                ],
                visible: false,
                width: 120
            },
            {
                field: 'recordDate',
                label: 'Record Date',
                format: 'datetime',
                sort: 'desc',
                width: 150
            },
            {
                field: 'phone',
                label: 'Phone',
                phoneCell: true,
                width: 150
            },
            {
                field: 'activityCount',
                label: 'Activities',
                relationParams(params) {
                    if (_.isPlainObject(params.data) && _.isNumber(params.value)) {
                        return {
                            view: 'crm.reports.activity-analysis',

                            params: {
                                showDocumentActivities: true,
                                filters: {
                                    linkedDocumentId: params.data._id
                                },
                                scope: [
                                    {
                                        type: 'filter',
                                        field: 'linkedDocumentType',
                                        value: ['crm.leads']
                                    }
                                ]
                            }
                        };
                    }

                    return {};
                },
                width: 90
            },
            {
                field: 'organization',
                label: 'Organization',
                subSelect: ['code', 'name'],
                render(params) {
                    const data = params.data;

                    if (_.isObject(data) && _.isObject(data.organization)) {
                        return `${data.organization.code} - ${data.organization.name}`;
                    }

                    if (params.value) {
                        return params.value;
                    }

                    return '';
                },
                visible: false
            },
            {
                field: 'salesManager',
                label: 'Sales manager',
                subSelect: ['code', 'name'],
                relationParams(params) {
                    const data = params.data;
                    const relation = {};

                    relation.isVisible =
                        _.isObject(data.salesManager) &&
                        _.isString(data.salesManager.code) &&
                        _.isString(data.salesManager.name);

                    if (relation.isVisible) {
                        relation.view = 'partners.partners-detail';
                        relation.id = data.salesManagerId;
                        relation.template = '{{code}} - {{name}}';
                    }

                    return relation;
                },
                visible: false,
                width: 180
            },
            {
                field: 'salesperson',
                label: 'Salesperson',
                subSelect: ['code', 'name'],
                relationParams(params) {
                    const data = params.data;
                    const relation = {};

                    relation.isVisible =
                        _.isObject(data.salesperson) &&
                        _.isString(data.salesperson.code) &&
                        _.isString(data.salesperson.name);

                    if (relation.isVisible) {
                        relation.view = 'partners.partners-detail';
                        relation.id = data.salespersonId;
                        relation.template = '{{code}} - {{name}}';
                    }

                    return relation;
                },
                visible: false,
                width: 180
            },
            {
                field: 'status',
                label: 'Status',
                translateLabels: true,
                tagsCell: true,
                tagLabels: [
                    {value: 'draft', label: 'Draft', color: 'default'},
                    {
                        value: 'converted-to-opportunity',
                        label: 'Converted To Opportunity',
                        color: 'primary'
                    },
                    {
                        value: 'converted-to-quotation',
                        label: 'Converted To Quotation',
                        color: 'primary'
                    },
                    {
                        value: 'converted-to-order',
                        label: 'Converted To Order',
                        color: 'primary'
                    },
                    {
                        value: 'converted-to-customer',
                        label: 'Converted To Customer',
                        color: 'primary'
                    },
                    {value: 'canceled', label: 'Canceled', color: 'danger'}
                ],
                maxWidth: 180
            }
        ].concat(
            vm.divisions.map(d => ({
                field: `address.${d.code}`,
                label: d.name,
                width: 150,
                visible: false
            }))
        );
    } else if (type === 'opportunity') {
        const targetTime = targetTimes.find(tt => tt.document === 'opportunity');

        return [
            {
                field: 'target-time',
                label: 'Duration',
                cellRendererParams: {targetTime},
                cellRendererFramework: TargetTimeCellRenderer,
                cellClass: 'pl0 pr0',
                cellStyle: {border: 'none'},
                width: 90,
                hidden: !targetTime.isActive
            },
            {
                field: 'code',
                label: 'Code',
                relationParams(params) {
                    const data = params.data;
                    const relation = {};

                    relation.isVisible = _.isString(data._id) && _.isString(data.code);

                    if (relation.isVisible) {
                        relation.view = 'crm.customer-relations.opportunities-detail';
                        relation.id = data._id;
                    }

                    return relation;
                },
                width: 150
            },
            {field: 'name', label: 'Name', width: 150, visible: false},
            {
                field: 'lead.name',
                label: 'Lead',
                hidden: true
            },
            {
                field: 'partner.name',
                label: 'Customer',
                hidden: true
            },
            {
                field: 'customer-or-lead',
                label: 'Customer / Lead',
                relationParams(params) {
                    const data = params.data;

                    if (_.isPlainObject(data.partner) || _.isPlainObject(data.lead)) {
                        if (data.leadId) {
                            return {
                                view: 'crm.customer-relations.leads-detail',
                                id: data.leadId,
                                customLabel: data.lead.name
                            };
                        } else {
                            return {
                                view: 'partners.partners-detail',
                                id: data.partnerId,
                                customLabel: data.partner.name
                            };
                        }
                    }

                    return {isVisible: false};
                }
            },
            {
                field: 'probability',
                label: 'Probability',
                width: 210,
                progressCell: true
            },
            {
                field: 'currency.name',
                label: 'Currency',
                hidden: !vm.$setting('system.multiCurrency'),
                visible: false,
                width: 120
            },
            {
                field: 'potentialAmount',
                label: 'Potential amount',
                format: 'currency',
                formatOptions(row) {
                    if (_.isObject(row) && row.currencyId) {
                        const currency = vm.currencies.find(c => c._id === row.currencyId);
                        let options = {currency: {}};

                        options.currency.symbol = currency.symbol;
                        options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                        return options;
                    }

                    return {};
                },
                width: 150
            },
            {
                field: 'productsAmount',
                label: 'Products total amount',
                format: 'currency',
                formatOptions(row) {
                    if (_.isObject(row) && row.currencyId) {
                        const currency = vm.currencies.find(c => c._id === row.currencyId);
                        let options = {currency: {}};

                        options.currency.symbol = currency.symbol;
                        options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                        return options;
                    }

                    return {};
                },
                width: 150
            },
            {
                field: 'branch.name',
                label: 'Branch Office',
                hidden: !vm.$setting('system.multiBranch'),
                width: 180,
                visible: false
            },
            {
                field: 'qualificationLevel.name',
                label: 'Qualification level',
                visible: false
            },
            {field: 'source.name', label: 'Source', visible: false},
            {field: 'category.name', label: 'Category', visible: false},
            {
                field: 'communicationChannel.name',
                label: 'Communication channel',
                visible: false
            },
            {field: 'lostReason.name', label: 'Lost reason', visible: false},
            {
                field: 'priority',
                label: 'Priority',
                translateLabels: true,
                valueLabels: [
                    {value: 'not-urgent', label: 'Not urgent'},
                    {value: 'normal', label: 'Normal'},
                    {value: 'urgent', label: 'Urgent'},
                    {value: 'very-urgent', label: 'Very urgent'}
                ],
                visible: false,
                width: 120
            },
            {
                field: 'recordDate',
                label: 'Record Date',
                format: 'datetime',
                sort: 'desc',
                width: 150
            },
            {
                field: 'closingDate',
                label: 'Closing date',
                format: 'datetime',
                visible: false,
                width: 150
            },
            {
                field: 'phone',
                label: 'Phone',
                phoneCell: true,
                width: 150
            },
            {
                field: 'activityCount',
                label: 'Activities',
                relationParams(params) {
                    if (_.isPlainObject(params.data) && _.isNumber(params.value)) {
                        return {
                            view: 'crm.reports.activity-analysis',

                            params: {
                                showDocumentActivities: true,
                                filters: {
                                    linkedDocumentId: params.data._id
                                },
                                scope: [
                                    {
                                        type: 'filter',
                                        field: 'linkedDocumentType',
                                        value: ['crm.opportunities']
                                    }
                                ]
                            }
                        };
                    }

                    return {};
                },
                width: 90
            },
            {
                field: 'organization',
                label: 'Organization',
                subSelect: ['code', 'name'],
                render(params) {
                    const data = params.data;

                    if (_.isObject(data) && _.isObject(data.organization)) {
                        return `${data.organization.code} - ${data.organization.name}`;
                    }

                    if (params.value) {
                        return params.value;
                    }

                    return '';
                },
                visible: false
            },
            {
                field: 'salesManager',
                label: 'Sales manager',
                subSelect: ['code', 'name'],
                relationParams(params) {
                    const data = params.data;
                    const relation = {};

                    relation.isVisible =
                        _.isObject(data.salesManager) &&
                        _.isString(data.salesManager.code) &&
                        _.isString(data.salesManager.name);

                    if (relation.isVisible) {
                        relation.view = 'partners.partners-detail';
                        relation.id = data.salesManagerId;
                        relation.template = '{{code}} - {{name}}';
                    }

                    return relation;
                },
                visible: false,
                width: 180
            },
            {
                field: 'salesperson',
                label: 'Salesperson',
                subSelect: ['code', 'name'],
                relationParams(params) {
                    const data = params.data;
                    const relation = {};

                    relation.isVisible =
                        _.isObject(data.salesperson) &&
                        _.isString(data.salesperson.code) &&
                        _.isString(data.salesperson.name);

                    if (relation.isVisible) {
                        relation.view = 'partners.partners-detail';
                        relation.id = data.salespersonId;
                        relation.template = '{{code}} - {{name}}';
                    }

                    return relation;
                },
                visible: false,
                width: 180
            },
            {
                field: 'status',
                label: 'Status',
                translateLabels: true,
                tagsCell: true,
                tagLabels: [
                    {value: 'draft', label: 'Draft', color: 'default'},
                    {
                        value: 'processing',
                        label: 'Processing',
                        color: 'success'
                    },
                    {
                        value: 'lost-to-competitor',
                        label: 'Lost To Competitor',
                        color: 'orange'
                    },
                    {value: 'lost', label: 'Lost', color: 'warning'},
                    {value: 'won', label: 'Won', color: 'primary'},
                    {value: 'canceled', label: 'Canceled', color: 'danger'}
                ],
                maxWidth: 180
            }
        ].concat(
            vm.divisions.map(d => ({
                field: `address.${d.code}`,
                label: d.name,
                width: 150,
                visible: false
            }))
        );
    } else if (type === 'quotation') {
        const targetTime = targetTimes.find(tt => tt.document === 'quotation');

        return [
            {
                field: 'target-time',
                label: 'Duration',
                cellRendererParams: {targetTime},
                cellRendererFramework: TargetTimeCellRenderer,
                cellClass: 'pl0 pr0',
                cellStyle: {border: 'none'},
                width: 90,
                hidden: !targetTime.isActive
            },
            {
                field: 'code',
                label: 'Code',
                relationParams(params) {
                    const data = params.data;
                    const relation = {};

                    relation.isVisible = _.isString(data._id) && _.isString(data.code);

                    if (relation.isVisible) {
                        relation.view = 'sale.sales.quotations-detail';
                        relation.id = data._id;
                    }

                    return relation;
                },
                width: 150
            },
            {
                field: 'lead.name',
                label: 'Lead',
                hidden: true
            },
            {
                field: 'partner.name',
                label: 'Customer',
                hidden: true
            },
            {
                field: 'customer-or-lead',
                label: 'Customer / Lead',
                relationParams(params) {
                    const data = params.data;

                    if (data.leadId && !!data.lead) {
                        return {
                            view: 'crm.customer-relations.leads-detail',
                            id: data.leadId,
                            customLabel: data.lead.name
                        };
                    } else if (data.partnerId && !!data.partner) {
                        return {
                            view: 'partners.partners-detail',
                            id: data.partnerId,
                            customLabel: data.partner.name
                        };
                    }
                }
            },
            {
                field: 'probability',
                label: 'Probability',
                width: 210,
                progressCell: true
            },
            {
                field: 'currency.name',
                label: 'Currency',
                hidden: !vm.$setting('system.multiCurrency'),
                visible: false,
                width: 120
            },
            {
                field: 'subTotalAfterDiscount',
                label: 'Subtotal',
                format: 'currency',
                visible: false,
                width: 120
            },
            {
                field: 'discount',
                label: 'Discount %',
                format: 'percentage',
                visible: false,
                width: 90
            },
            {
                field: 'discountAmount',
                label: 'Discount',
                format: 'currency',
                visible: false,
                width: 120
            },
            {
                field: 'taxTotal',
                label: 'Tax Total',
                format: 'currency',
                visible: false,
                width: 120
            },
            {
                field: 'grandTotal',
                label: 'Total',
                format: 'currency',
                width: 120
            },
            {
                field: 'branch.name',
                label: 'Branch Office',
                hidden: !vm.$setting('system.multiBranch'),
                width: 180,
                visible: false
            },
            {
                field: 'communicationChannel.name',
                label: 'Communication channel',
                visible: false
            },
            {field: 'source.name', label: 'Source', visible: false},
            {field: 'lostReason.name', label: 'Lost reason', visible: false},
            {
                field: 'quotationDate',
                label: 'Quotation Date',
                format: 'date',
                sort: 'desc',
                width: 120
            },
            {
                field: 'expiryDate',
                label: 'Expiry date',
                format: 'date',
                width: 120
            },
            {
                field: 'activityCount',
                label: 'Activities',
                relationParams(params) {
                    if (_.isPlainObject(params.data) && _.isNumber(params.value)) {
                        return {
                            view: 'crm.reports.activity-analysis',

                            params: {
                                showDocumentActivities: true,
                                filters: {
                                    linkedDocumentId: params.data._id
                                },
                                scope: [
                                    {
                                        type: 'filter',
                                        field: 'linkedDocumentType',
                                        value: ['sale.quotations']
                                    }
                                ]
                            }
                        };
                    }

                    return {};
                },
                width: 90
            },
            {
                field: 'organization',
                label: 'Organization',
                subSelect: ['code', 'name'],
                render(params) {
                    const data = params.data;

                    if (_.isObject(data) && _.isObject(data.organization)) {
                        return `${data.organization.code} - ${data.organization.name}`;
                    }

                    if (params.value) {
                        return params.value;
                    }

                    return '';
                },
                visible: false
            },
            {
                field: 'salesManager',
                label: 'Sales manager',
                subSelect: ['code', 'name'],
                relationParams(params) {
                    const data = params.data;
                    const relation = {};

                    relation.isVisible =
                        _.isObject(data.salesManager) &&
                        _.isString(data.salesManager.code) &&
                        _.isString(data.salesManager.name);

                    if (relation.isVisible) {
                        relation.view = 'partners.partners-detail';
                        relation.id = data.salesManagerId;
                        relation.template = '{{code}} - {{name}}';
                    }

                    return relation;
                },
                visible: false,
                width: 180
            },
            {
                field: 'salesperson',
                label: 'Salesperson',
                subSelect: ['code', 'name'],
                relationParams(params) {
                    const data = params.data;
                    const relation = {};

                    relation.isVisible =
                        _.isObject(data.salesperson) &&
                        _.isString(data.salesperson.code) &&
                        _.isString(data.salesperson.name);

                    if (relation.isVisible) {
                        relation.view = 'partners.partners-detail';
                        relation.id = data.salespersonId;
                        relation.template = '{{code}} - {{name}}';
                    }

                    return relation;
                },
                visible: false,
                width: 180
            },
            {
                field: 'status',
                label: 'Status',
                translateLabels: true,
                tagsCell: true,
                tagLabels: [
                    {value: 'draft', label: 'Draft', color: 'default'},
                    {
                        value: 'payment-planned',
                        label: 'Payment Planned',
                        color: 'success'
                    },
                    {
                        value: 'lost-to-competitor',
                        label: 'Lost To Competitor',
                        color: 'orange'
                    },
                    {value: 'lost', label: 'Lost', color: 'warning'},
                    {value: 'won', label: 'Won', color: 'primary'},
                    {value: 'canceled', label: 'Canceled', color: 'danger'}
                ],
                maxWidth: 180
            }
        ].concat(
            vm.divisions.map(d => ({
                field: `deliveryAddress.${d.code}`,
                label: d.name,
                width: 150,
                visible: false
            }))
        );
    }

    return [];
}
