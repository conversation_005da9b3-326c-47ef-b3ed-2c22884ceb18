<template>
    <ui-view
        type="table"
        collection="kernel.activities"
        :columns="columns"
        :filters="filters"
        :extra-fields="extraFields"
        actions="create,remove"
        v-if="initialized"
    >
        <template slot="top-panel">
            <ui-scope
                id="calendar.activities"
                :filters="scopeApplicableFilters"
                :applied-items="scopeAppliedItems"
                @changed="handleScopeChange"
            />
        </template>
    </ui-view>
</template>

<script>
import _ from 'lodash';

const types = [
    {value: 'task', label: 'Task', color: 'blue', icon: 'tasks'},
    {value: 'meeting', label: 'Meeting', color: 'green', icon: 'presentation'},
    {value: 'appointment', label: 'Appointment', color: 'orange', icon: 'tags'},
    {value: 'phone-call', label: 'Phone Call', color: 'purple', icon: 'phone-volume'},
    {value: 'event', label: 'Event', color: 'pink', icon: 'calendar-alt'}
];

export default {
    data: () => ({
        extraFields: [
            'color',
            'phoneCallDurationMinutes',
            'phoneCallDurationSeconds',
            'assignedToType',
            'params.assignedTo'
        ],
        scopeQuery: {},
        type: null,
        initialized: false
    }),

    computed: {
        filters() {
            let filters = _.cloneDeep(this.scopeQuery);

            const userId = this.$user._id;

            if (!filters.$or) {
                filters.$or = [];
            }

            filters.$or.push({isPersonal: false}, {isPersonal: true, assignedById: userId});

            if (!this.$params('inDialog')) {
                filters.$disableActiveCheck = true;
            }

            return filters;
        },
        columns() {
            const self = this;

            return [
                {
                    field: 'code',
                    label: 'Code',
                    width: 100
                },
                {
                    field: 'startDate',
                    label: 'Start date',
                    sort: 'desc',
                    format: 'datetime',
                    width: 150,
                    hidden: this.type === 'phone-call'
                },
                {
                    field: 'endDate',
                    label: 'End date',
                    format: 'datetime',
                    width: 150,
                    visible: this.type === 'task',
                    hidden: this.type === 'phone-call'
                },
                {
                    field: 'phoneCallStartTime',
                    label: 'Phone call start time',
                    format: 'datetime',
                    width: 150,
                    hidden: this.type && this.type !== 'phone-call',
                    visible: this.type === 'phone-call'
                },
                {
                    field: 'type',
                    label: 'Type',
                    render(params) {
                        const data = params.data;

                        if (_.isObject(data) && data.type) {
                            const type = types.find(type => type.value === data.type);
                            const label = self.$t(type.label);
                            const color = self.$app.colors[type.color];

                            return `<span style="display: inline-block; width: 10px; height: 10px;margin-right: 5px; border-radius: 50%; background-color: ${color}"></span>${label}`;
                        }

                        return '';
                    },
                    width: 150,
                    hidden: !!this.type
                },
                {
                    field: 'subject',
                    label: 'Subject'
                },
                {
                    field: 'description',
                    label: 'Description',
                    width: 150
                },
                {
                    field: 'phoneCallType',
                    label: 'Phone call type',
                    translateLabels: true,
                    valueLabels: [
                        {value: 'completed', label: 'Completed phone call'},
                        {value: 'planned', label: 'Planned phone call'}
                    ],
                    hidden: this.type !== 'phone-call',
                    width: 150
                },
                {
                    field: 'phoneCallDirection',
                    label: 'Phone call direction',
                    translateLabels: true,
                    valueLabels: [
                        {value: 'outbound', label: 'Outbound phone call'},
                        {value: 'inbound', label: 'Inbound phone call'}
                    ],
                    hidden: this.type && this.type !== 'phone-call',
                    visible: this.type === 'phone-call',
                    width: 120
                },
                {
                    field: 'phoneCallDuration',
                    label: 'Phone call duration',
                    render(params) {
                        const data = params.data;

                        if (
                            _.isObject(data) &&
                            _.isInteger(data.phoneCallDurationMinutes) &&
                            _.isInteger(data.phoneCallDurationSeconds)
                        ) {
                            return self.$t('{{minute}} min {{second}} sec', {
                                minute: data.phoneCallDurationMinutes,
                                second: data.phoneCallDurationSeconds
                            });
                        }

                        return '';
                    },
                    hidden: this.type && this.type !== 'phone-call',
                    visible: this.type === 'phone-call',
                    width: 150
                },
                {
                    field: 'phoneCallResult',
                    label: 'Phone call result',
                    hidden: this.type && this.type !== 'phone-call',
                    visible: this.type === 'phone-call'
                },
                {
                    field: 'lead.name',
                    label: 'Lead',
                    hidden: true
                },
                {
                    field: 'partner.name',
                    label: 'Partner',
                    hidden: true
                },
                {
                    field: 'partner-or-lead',
                    label: 'Partner',
                    relationParams(params) {
                        const data = params.data;

                        if (data.leadId && _.isPlainObject(data.lead)) {
                            return {
                                view: 'crm.customer-relations.leads-detail',
                                id: data.leadId,
                                customLabel: data.lead.name
                            };
                        } else if (data.partnerId && _.isPlainObject(data.partner)) {
                            return {
                                view: 'partners.partners-detail',
                                id: data.partnerId,
                                customLabel: data.partner.name
                            };
                        }

                        return {};
                    },
                    visible: this.type !== 'task',
                    width: 180
                },
                {
                    field: 'meetingLocation',
                    label: 'Meeting location',
                    render(params) {
                        const data = params.data;

                        if (_.isObject(data) && _.isObject(data.meetingLocation)) {
                            return `${data.meetingLocation.code} - ${data.meetingLocation.name}`;
                        }

                        return '';
                    },
                    hidden: this.type === 'task' || this.type === 'phone-call',
                    width: 180
                },
                {
                    field: 'assignedBy',
                    label: 'Assigned by',
                    subSelect: ['code', 'name'],
                    relationParams(params) {
                        const data = params.data;
                        const relation = {};

                        relation.isVisible =
                            _.isObject(data.assignedBy) &&
                            _.isString(data.assignedBy.code) &&
                            _.isString(data.assignedBy.name);

                        if (relation.isVisible) {
                            relation.view = 'partners.partners-detail';
                            relation.id = data.assignedById;
                            relation.template = '{{code}} - {{name}}';
                        }

                        return relation;
                    },
                    visible: this.type === 'task',
                    width: 180
                },
                {
                    field: 'assignedTo',
                    label: 'Assigned to',
                    relationParams(params) {
                        const data = params.data;
                        const {assignedTo} = data.params || {};
                        const relation = {};

                        relation.isVisible =
                            _.isObject(assignedTo) && _.isString(assignedTo.code) && _.isString(assignedTo.name);

                        if (relation.isVisible) {
                            if (data.assignedToType === 'employee') {
                                relation.view = 'partners.partners-detail';
                                relation.id = assignedTo.id;
                                relation.customLabel = `${assignedTo.code} - ${assignedTo.name}`;
                            } else {
                                relation.view = 'system.members.users-detail';
                                relation.id = assignedTo.id;
                                relation.customLabel = `${assignedTo.code} - ${assignedTo.name}`;
                            }
                        }

                        return relation;
                    },
                    visible: this.type === 'task',
                    width: 180
                },
                {
                    field: 'priority',
                    label: 'Priority',
                    translateLabels: true,
                    valueLabels: [
                        {value: 'not-urgent', label: 'Not urgent'},
                        {value: 'normal', label: 'Normal'},
                        {value: 'urgent', label: 'Urgent'},
                        {value: 'very-urgent', label: 'Very urgent'}
                    ],
                    width: 120,
                    visible: this.type !== 'phone-call'
                },
                {
                    field: 'status',
                    label: 'Status',
                    tagsCell: true,
                    translateLabels: true,
                    tagLabels: [
                        {value: 'open', label: 'Open', color: 'default'},
                        {value: 'stopped', label: 'Stopped', color: 'warning'},
                        {value: 'started', label: 'Started', color: 'success'},
                        {value: 'canceled', label: 'Canceled', color: 'danger'},
                        {value: 'closed', label: 'Closed', color: 'primary'}
                    ],
                    width: 150,
                    visible: this.type !== 'phone-call'
                }
            ];
        },
        scopeAppliedItems() {
            return [{type: 'filter', payload: 'thisMonth'}];
        },
        scopeApplicableFilters() {
            const self = this;

            return [
                // Pre-defined
                {code: 'today', label: 'Today', query: 'startDate|today'},
                {code: 'thisWeek', label: 'This week', query: 'startDate|thisWeek'},
                {code: 'lastWeek', label: 'Last week', query: 'startDate|lastWeek'},
                {code: 'thisMonth', label: 'This month', query: 'startDate|thisMonth'},
                {code: 'lastMonth', label: 'Last month', query: 'startDate|lastMonth'},

                // Filters.
                {field: 'code', label: 'Code'},
                {
                    field: 'startDate',
                    code: 'startDate',
                    label: 'Start date',
                    type: 'datetime',
                    condition: () => this.type !== 'phone-call'
                },
                {
                    field: 'endDate',
                    code: 'endDate',
                    label: 'End date',
                    type: 'datetime',
                    condition: () => this.type !== 'phone-call'
                },
                {
                    field: 'phoneCallStartTime',
                    code: 'phoneCallStartTime',
                    label: 'Phone call start time',
                    type: 'datetime',
                    condition: () => !this.type || this.type === 'phone-call'
                },
                {
                    code: 'type',
                    field: 'type',
                    label: 'Type',
                    translateLabels: true,
                    valueLabels: [
                        {value: 'task', label: 'Task'},
                        {value: 'meeting', label: 'Meeting'},
                        {value: 'appointment', label: 'Appointment'},
                        {value: 'phone-call', label: 'Phone Call'},
                        {value: 'event', label: 'Event'}
                    ],
                    condition: () => !this.type
                },
                {
                    field: 'partnerId',
                    label: 'Partner',
                    collection: 'kernel.partners',
                    extraFields: ['code'],
                    filters: {$sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'leadId',
                    label: 'Lead',
                    collection: 'crm.leads',
                    extraFields: ['code'],
                    filters: {$sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'phoneCallType',
                    label: 'Phone call type',
                    translateLabels: true,
                    items: [
                        {value: 'completed', label: 'Completed phone call'},
                        {value: 'planned', label: 'Planned phone call'}
                    ],
                    condition: () => !this.type || this.type === 'phone-call'
                },
                {
                    field: 'phoneCallDirection',
                    label: 'Phone call direction',
                    translateLabels: true,
                    items: [
                        {value: 'outbound', label: 'Outbound phone call'},
                        {value: 'inbound', label: 'Inbound phone call'}
                    ],
                    condition: () => !this.type || this.type === 'phone-call'
                },
                {
                    field: 'phoneCallDurationMinutes',
                    label: 'Phone call duration (Minute)',
                    type: 'integer',
                    condition: () => !this.type || this.type === 'phone-call'
                },
                {
                    field: 'phoneCallResult',
                    label: 'Phone call result',
                    condition: () => !this.type || this.type === 'phone-call'
                },
                {
                    field: 'meetingLocationId',
                    label: 'Meeting location',
                    collection: 'kernel.meeting-locations',
                    extraFields: ['code'],
                    filters: {$sort: {name: 1}},
                    template: '{{code}} - {{name}}',
                    condition: () => this.type !== 'task'
                },
                {
                    field: 'assignedById',
                    label: 'Assigned by',
                    collection: 'kernel.users',
                    extraFields: ['code'],
                    filters: {$sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'params.assignedTo.id',
                    code: 'assignedToEmployeeId',
                    label: 'Assigned to employee',
                    collection: 'kernel.partners',
                    extraFields: ['code'],
                    filters: {type: 'employee', $sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    field: 'params.assignedTo.id',
                    code: 'assignedToUserId',
                    label: 'Assigned to user',
                    collection: 'kernel.users',
                    extraFields: ['code'],
                    filters: {$sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {
                    code: 'status',
                    field: 'status',
                    label: 'Status',
                    translateLabels: true,
                    valueLabels: [
                        {value: 'open', label: 'Open'},
                        {value: 'started', label: 'Started'},
                        {value: 'stopped', label: 'Stopped'},
                        {value: 'closed', label: 'Closed'}
                    ]
                }
            ];
        }
    },

    methods: {
        handleScopeChange(model) {
            this.scopeQuery = model.query;
        }
    },

    async created() {
        if (this.$params('filters.type')) {
            this.type = this.$params('filters.type');
        }

        this.initialized = true;
    }
};
</script>
