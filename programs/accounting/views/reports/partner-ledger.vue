<template>
    <ui-view
        class="accounting-partner-ledger"
        :extra-actions="extraActions"
        :left-panel-width="370"
        progress-id="accounting.reports.partner-ledger"
        v-if="initialized"
    >
        <template slot="left-panel">
            <ui-list
                ref="partnerList"
                :get-rows="getPartners"
                :enable-search="true"
                label-from="partner.name"
                :extra-fields="['partner.code', 'partner.name']"
                :item-height="60"
                :html-template="getPartnersCellTemplate"
                single-select
                @selected-items="handlePartnerSelect"
            />
        </template>

        <template v-if="!!selectedPartner">
            <div
                :key="`${selectedPartner._id}-${isConnectedPartnerShown}`"
                class="accounting-partner-ledger-records-container"
            >
                <div class="accounting-partner-ledger-records-header">
                    <div class="selected-partner-info">
                        <div class="selected-partner-avatar-container">
                            <div :class="selectedPartnerAvatarClasses">
                                {{ selectedPartnerShortName }}
                            </div>
                        </div>

                        <div class="selected-partner-content">
                            <div class="selected-partner-name">
                                {{ selectedPartner.name }}
                            </div>
                            <div class="selected-partner-details">
                                {{ selectedPartner.code }} - {{ selectedPartnerType }}
                            </div>
                        </div>
                    </div>

                    <div class="selected-partner-reports">
                        <div class="selected-partner-report">
                            <div class="report-label">{{ 'Debit' | t }}</div>
                            <div class="report-value">{{ $format(selectedPartner.debit, 'currency') }}</div>
                        </div>

                        <div class="selected-partner-report">
                            <div class="report-label">{{ 'Credit' | t }}</div>
                            <div class="report-value">{{ $format(selectedPartner.credit, 'currency') }}</div>
                        </div>

                        <div class="selected-partner-report">
                            <div class="report-label">{{ 'Balance' | t }}</div>
                            <div class="report-value">{{ $format(selectedPartner.balance, 'currency') }}</div>
                        </div>
                    </div>
                </div>

                <div class="accounting-partner-ledger-records-container">
                    <div class="records-top">
                        <div class="records-top-actions">
                            <el-button
                                size="mini"
                                type="primary"
                                icon="far fa-cloud-download"
                                @click="() => handleExport(selectedPartner)"
                            >
                                {{ 'Export' | t }}
                            </el-button>
                        </div>

                        <ui-scope
                            :id="`accounting.reports.partner-ledger-${!!type ? type : 'all'}`"
                            :filters="recordsScopeApplicableFilters"
                            @changed="handleRecordsScopeChange"
                        />

                        <div class="records-top-search">
                            <el-input
                                v-model="recordsSearchQuery"
                                class="relation-search"
                                :placeholder="'Search..' | t"
                                prefix-icon="el-icon-search"
                                clearable
                                size="mini"
                            >
                            </el-input>
                        </div>
                    </div>
                    <div class="records-content">
                        <ui-table
                            :id="`accounting.reports.general-ledger-${!!type ? type : 'all'}`"
                            :items="records"
                            :columns="recordsColumns"
                            :search="recordsSearchQuery"
                            :extra-fields="recordsExtraFields"
                            :enable-sorting="false"
                            :enable-selection="true"
                            :options="recordsTableOptions"
                        />
                    </div>
                </div>
            </div>
        </template>
        <el-empty-state class="accounting-partner-ledger-empty-state" v-else />
    </ui-view>
</template>

<script>
import _ from 'lodash';
import fastCopy from 'fast-copy';
import {rawMongoQuery, toUpper} from 'framework/helpers';
import PrintPopup from '@/kernel/frontend/components/ui/view/print-popup';

export default {
    data: () => ({
        selectedPartner: null,
        recordsSearchQuery: '',
        recordsScopeQuery: {},
        recordsExtraFields: [
            'entryId',
            'accountId',
            'partnerId',
            'referenceId',
            'referenceView',
            'belongsToOpeningRecord',
            'belongsToClosingRecord'
        ],
        records: [],
        currencies: [],
        isConnectedPartnerShown: false,
        recordsTableOptions: {
            rowHeight: 36
        },
        initialized: false
    }),

    computed: {
        type() {
            return this.$params('type') || null;
        },
        extraActions() {
            return [
                ...(this.isConnectedPartnerShown
                    ? [
                          {
                              name: 'exclude-connected-partner-transactions',
                              title: 'Exclude connected partner transactions',
                              icon: 'fal fa-eye-slash',
                              handler: this.handleToggleConnectedPartner
                          }
                      ]
                    : [
                          {
                              name: 'include-connected-partner-transactions',
                              title: 'Include connected partner transactions',
                              icon: 'fal fa-eye',
                              handler: this.handleToggleConnectedPartner
                          }
                      ]),
                {
                    name: 'sync-balances',
                    title: 'Sync Partner Balances',
                    icon: 'fal fa-sync',
                    handler: this.handleSyncBalances
                }
                // {
                //     name: 'export',
                //     title: 'Export',
                //     icon: 'fal fa-cloud-download',
                //     handler: this.handleExport
                // }
            ];
        },
        selectedPartnerShortName() {
            return ((this.selectedPartner || {}).name || '')
                .split(' ')
                .map(s => toUpper(s[0]))
                .slice(0, 3)
                .join('');
        },
        selectedPartnerType() {
            const type = (this.selectedPartner || {}).type || 'customer';

            return type === 'customer'
                ? this.$t('Customer')
                : type === 'vendor'
                ? this.$t('Vendor')
                : this.$t('Employee');
        },
        selectedPartnerAvatarClasses() {
            const type = (this.selectedPartner || {}).type || 'customer';
            const classes = ['selected-partner-avatar'];

            if (type === 'customer') {
                classes.push('is-customer');
            } else if (type === 'vendor') {
                classes.push('is-vendor');
            } else if (type === 'employee') {
                classes.push('is-employee');
            }

            return classes;
        },
        recordsFilters() {
            const filters = fastCopy(this.recordsScopeQuery);
            // const searchQuery = trim(this.recordsSearchQuery, ' ');

            filters.partnerId = this.selectedPartner._id;

            if (!!this.type) {
                filters['partner.type'] = this.type;
            }

            // if (!!searchQuery) {
            //     if (this.$setting('system.searchMethod') === 'full-text') {
            //         if (!Array.isArray(filters.$select)) {
            //             filters.$select = [];
            //         }
            //         filters.$text = {
            //             $search: `"${toLower(searchQuery)}"`
            //         };
            //         filters.$select.push({confidenceScore: {$meta: 'textScore'}});
            //         filters.$sort = {confidenceScore: {$meta: 'textScore'}};
            //     } else {
            //         if (!filters.$and) {
            //             filters.$and = [];
            //         }
            //
            //         filters.$and.push(getMongoSearchQuery('searchText', toLower(searchQuery)));
            //     }
            // }

            return filters;
        },
        recordsScopeApplicableFilters() {
            const self = this;

            const scopeOptions = [
                {value: '1', label: this.$setting('system.scope1Label')},
                {value: '2', label: this.$setting('system.scope2Label')}
            ];

            let partnerLabel = 'Partner';
            if (!!this.type) {
                if (this.type === 'customer') {
                    partnerLabel = 'Customer';
                } else if (this.type === 'vendor') {
                    partnerLabel = 'Vendor';
                } else if (this.type === 'employee') {
                    partnerLabel = 'Employee';
                }
            }

            return [
                // Pre-defined
                {code: 'today', label: 'Today', query: 'issueDate|today'},
                {code: 'yesterday', label: 'Yesterday', query: 'issueDate|yesterday'},
                {code: 'thisWeek', label: 'This week', query: 'issueDate|thisWeek'},
                {code: 'lastWeek', label: 'Last week', query: 'issueDate|lastWeek'},
                {code: 'thisMonth', label: 'This month', query: 'issueDate|thisMonth'},
                {code: 'lastMonth', label: 'Last month', query: 'issueDate|lastMonth'},
                {code: 'thisQuarter', label: 'This quarter', query: 'issueDate|thisQuarter'},
                {code: 'lastQuarter', label: 'Last quarter', query: 'issueDate|lastQuarter'},

                // Filters.
                {
                    field: 'partner.tinIdentity',
                    label: 'TIN / Identity'
                },
                {field: 'entryNo', label: 'Entry no', type: 'integer'},
                {field: 'account.code', label: 'Account code', type: 'text'},
                {
                    field: 'recordDate',
                    code: 'recordDate',
                    label: 'Record date',
                    type: 'date'
                },
                {
                    field: 'issueDate',
                    code: 'issueDate',
                    label: 'Issue date',
                    type: 'date'
                },
                {
                    field: 'dueDate',
                    code: 'dueDate',
                    label: 'Due date',
                    type: 'date'
                },
                {field: 'documentNo', label: 'Document no'},
                {field: 'voucherNo', label: 'Voucher no'},
                {field: 'chequeTrackingCode', label: 'Cheque tracking code'},
                {
                    field: 'journalId',
                    label: 'Journal',
                    collection: 'accounting.journals',
                    filters: {
                        $sort: {name: 1}
                    }
                },
                {
                    field: 'journal.type',
                    label: 'Journal type',
                    translateLabels: true,
                    valueLabels: [
                        {label: 'Sale', value: 'sale'},
                        {label: 'Purchase', value: 'purchase'},
                        {label: 'Sale return', value: 'sale-return'},
                        {label: 'Purchase return', value: 'purchase-return'},
                        {label: 'Cash safe', value: 'cash-safe'},
                        {label: 'Bank', value: 'bank'},
                        {label: 'Cheque', value: 'cheque'},
                        {label: 'Promissory note', value: 'promissory-note'},
                        {label: 'Credit card', value: 'credit-card'},
                        {label: 'POS', value: 'pos'},
                        {label: 'Guarantee', value: 'guarantee'},
                        {label: 'Loan', value: 'loan'},
                        {label: 'Stock', value: 'stock'},
                        {label: 'Salary', value: 'salary'},
                        {label: 'Expense', value: 'expense'},
                        {label: 'Income', value: 'income'},
                        {label: 'Opening record', value: 'opening-record'},
                        {label: 'Closing record', value: 'closing-record'},
                        {label: 'Period end', value: 'period-end'},
                        {label: 'Exchange Rate Difference', value: 'exchange-rate-difference'},
                        {label: 'Depreciation', value: 'depreciation'},
                        {label: 'Miscellaneous', value: 'miscellaneous'}
                    ]
                },
                {
                    field: 'branchId',
                    label: 'Branch office',
                    collection: 'kernel.branches',
                    filters: {
                        _id: {$in: this.$user.branchIds},
                        $sort: {name: 1}
                    },
                    condition() {
                        return self.$setting('system.multiBranch');
                    }
                },
                {
                    field: 'financialProjectId',
                    label: 'Project',
                    collection: 'kernel.financial-projects',
                    extraFields: ['code'],
                    filters: {$sort: {name: 1}},
                    template: '{{code}} - {{name}}'
                },
                {field: 'reconciliationCode', label: 'Reconciliation'},
                {field: 'reference', label: 'Reference'},
                {field: 'description', label: 'Description'},
                {
                    field: 'currencyId',
                    label: 'Currency',
                    collection: 'kernel.currencies',
                    filters: {$sort: {name: 1}},
                    condition() {
                        return self.$setting('system.multiCurrency');
                    }
                },
                {
                    field: 'period.fiscalYearId',
                    label: 'Fiscal year',
                    collection: 'kernel.periods',
                    singleSelect: true
                },
                {
                    field: 'scope',
                    label: 'Scope',
                    condition() {
                        return self.$setting('system.scopes');
                    },
                    valueLabels: scopeOptions
                }
            ];
        },
        recordsColumns() {
            const self = this;

            const scopeOptions = [
                {value: '1', label: this.$setting('system.scope1Label')},
                {value: '2', label: this.$setting('system.scope2Label')}
            ];

            let partnerLabel = 'Partner';
            if (!!this.type) {
                if (this.type === 'customer') {
                    partnerLabel = 'Customer';
                } else if (this.type === 'vendor') {
                    partnerLabel = 'Vendor';
                } else if (this.type === 'employee') {
                    partnerLabel = 'Employee';
                }
            }

            return [
                {
                    field: 'issueDate',
                    label: 'Issue Date',
                    format: 'date',
                    sortable: false,
                    width: 120
                },
                {
                    field: 'dueDate',
                    label: 'Due date',
                    format: 'date',
                    sortable: false,
                    width: 120
                },
                {
                    field: 'documentNo',
                    label: 'Document No',
                    sortable: false,
                    width: 150
                },
                {
                    field: 'partner.tinIdentity',
                    label: 'TIN / Identity',
                    width: 150,
                    sortable: false,
                    visible: false
                },
                {
                    field: 'account.code',
                    label: 'Account code',
                    sortable: false,
                    width: 150
                },
                {
                    field: 'account.name',
                    label: 'Account name',
                    sortable: false,
                    minWidth: 180
                },
                {
                    field: 'voucherNo',
                    label: 'Voucher No',
                    relationParams(params) {
                        const data = params.data;
                        let view = 'accounting.adviser.journal-entries-detail';

                        if (data.belongsToOpeningRecord) {
                            view = 'accounting.adviser.opening-records-detail';
                        } else if (data.belongsToClosingRecord) {
                            view = 'accounting.adviser.closing-records-detail';
                        }

                        return {
                            id: data.entryId,
                            view
                        };
                    },
                    visible: false,
                    sortable: false,
                    width: 120
                },
                {
                    field: 'chequeTrackingCode',
                    label: 'Cheque tracking code',
                    width: 150,
                    sortable: false,
                    visible: false
                },
                {
                    field: 'journal.name',
                    label: 'Journal',
                    sortable: false,
                    width: 150,
                    visible: false
                },
                {
                    field: 'journal.type',
                    label: 'Journal type',
                    translateLabels: true,
                    valueLabels: [
                        {label: 'Sale', value: 'sale'},
                        {label: 'Purchase', value: 'purchase'},
                        {label: 'Sale return', value: 'sale-return'},
                        {label: 'Purchase return', value: 'purchase-return'},
                        {label: 'Cash safe', value: 'cash-safe'},
                        {label: 'Bank', value: 'bank'},
                        {label: 'Cheque', value: 'cheque'},
                        {label: 'Promissory note', value: 'promissory-note'},
                        {label: 'Credit card', value: 'credit-card'},
                        {label: 'POS', value: 'pos'},
                        {label: 'Guarantee', value: 'guarantee'},
                        {label: 'Loan', value: 'loan'},
                        {label: 'Stock', value: 'stock'},
                        {label: 'Salary', value: 'salary'},
                        {label: 'Expense', value: 'expense'},
                        {label: 'Income', value: 'income'},
                        {label: 'Exchange Rate Difference', value: 'exchange-rate-difference'},
                        {label: 'Depreciation', value: 'depreciation'},
                        {label: 'Miscellaneous', value: 'miscellaneous'}
                    ],
                    visible: false,
                    sortable: false,
                    width: 150
                },
                {
                    field: 'financialProject',
                    label: 'Financial project',
                    render(params) {
                        let result = '';

                        if (_.isObject(params.value)) {
                            result = `${params.value.code} - ${params.value.name}`;
                        }

                        return result;
                    },
                    visible: false,
                    sortable: false,
                    width: 150
                },
                {
                    field: 'reconciliationCode',
                    label: 'Reconciliation',
                    visible: false,
                    sortable: false,
                    width: 150
                },
                {
                    field: 'description',
                    label: 'Description',
                    sortable: false,
                    minWidth: 180
                },
                {
                    field: 'reference',
                    label: 'Reference',
                    relationParams(params) {
                        const data = params.data;

                        if (!!data.referenceId && !!data.referenceView) {
                            return {
                                id: data.referenceId,
                                view: data.referenceView
                            };
                        }

                        return {};
                    },
                    sortable: false,
                    width: 150
                },
                {
                    field: 'currency.name',
                    label: 'Currency',
                    visible: false,
                    sortable: false,
                    width: 120
                },
                {
                    field: 'currencyRate',
                    label: 'Currency rate',
                    format: 'decimal',
                    visible: false,
                    sortable: false,
                    width: 120
                },
                {
                    field: 'scope',
                    label: 'Scope',
                    hidden: !this.$setting('system.scopes'),
                    width: 90,
                    visible: false,
                    sortable: false,
                    valueLabels: scopeOptions
                },
                {
                    field: 'scope1Debit',
                    label: this.$t('{{scope}} debit', {scope: this.$setting('system.scope1Label')}),
                    format: 'currency',
                    hidden: !this.$setting('system.scopes'),
                    visible: false,
                    valueGetter(params) {
                        if (!!params.data && params.data.scope === '1') {
                            return params.data.debit;
                        }

                        return 0;
                    },
                    sortable: false,
                    width: 150
                },
                {
                    field: 'scope1Credit',
                    label: this.$t('{{scope}} credit', {scope: this.$setting('system.scope1Label')}),
                    format: 'currency',
                    hidden: !this.$setting('system.scopes'),
                    visible: false,
                    valueGetter(params) {
                        if (!!params.data && params.data.scope === '1') {
                            return params.data.credit;
                        }

                        return 0;
                    },
                    sortable: false,
                    width: 150
                },
                {
                    field: 'scope2Debit',
                    label: this.$t('{{scope}} debit', {scope: this.$setting('system.scope2Label')}),
                    format: 'currency',
                    hidden: !this.$setting('system.scopes'),
                    visible: false,
                    valueGetter(params) {
                        if (!!params.data && params.data.scope === '2') {
                            return params.data.debit;
                        }

                        return 0;
                    },
                    sortable: false,
                    width: 150
                },
                {
                    field: 'scope2Credit',
                    label: this.$t('{{scope}} credit', {scope: this.$setting('system.scope2Label')}),
                    format: 'currency',
                    hidden: !this.$setting('system.scopes'),
                    visible: false,
                    valueGetter(params) {
                        if (!!params.data && params.data.scope === '2') {
                            return params.data.credit;
                        }

                        return 0;
                    },
                    sortable: false,
                    width: 150
                },
                {
                    field: 'initialBalance',
                    label: 'Initial Balance',
                    format: 'currency',
                    visible: false,
                    sortable: false,
                    width: 150
                },
                {
                    field: 'debit',
                    label: 'Debit',
                    format: 'currency',
                    sortable: false,
                    width: 150
                },
                {
                    field: 'credit',
                    label: 'Credit',
                    format: 'currency',
                    sortable: false,
                    width: 150
                },
                {
                    field: 'balance',
                    label: 'Balance',
                    format: 'currency',
                    pinned: 'right',
                    sortable: false,
                    width: 150
                },
                {
                    field: 'initialBalanceFC',
                    label: 'Initial balance (FC)',
                    format: 'currency',
                    visible: false,
                    formatOptions(row) {
                        if (_.isObject(row) && row.currencyId) {
                            const currency = self.currencies.find(c => c._id === row.currencyId);
                            let options = {currency: {}};

                            options.currency.symbol = currency.symbol;
                            options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                            return options;
                        }

                        return {};
                    },
                    sortable: false,
                    width: 150
                },
                {
                    field: 'debitFC',
                    label: 'Debit (FC)',
                    format: 'currency',
                    formatOptions(row) {
                        if (_.isObject(row) && row.currencyId) {
                            const currency = self.currencies.find(c => c._id === row.currencyId);
                            let options = {currency: {}};

                            options.currency.symbol = currency.symbol;
                            options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                            return options;
                        }

                        return {};
                    },
                    visible: false,
                    sortable: false,
                    width: 150
                },
                {
                    field: 'creditFC',
                    label: 'Credit (FC)',
                    format: 'currency',
                    formatOptions(row) {
                        if (_.isObject(row) && row.currencyId) {
                            const currency = self.currencies.find(c => c._id === row.currencyId);
                            let options = {currency: {}};

                            options.currency.symbol = currency.symbol;
                            options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                            return options;
                        }

                        return {};
                    },
                    visible: false,
                    sortable: false,
                    width: 150
                },
                {
                    field: 'balanceFC',
                    label: 'Balance (FC)',
                    format: 'currency',
                    formatOptions(row) {
                        if (_.isObject(row) && row.currencyId) {
                            const currency = self.currencies.find(c => c._id === row.currencyId);
                            let options = {currency: {}};

                            options.currency.symbol = currency.symbol;
                            options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                            return options;
                        }

                        return {};
                    },
                    pinned: 'right',
                    visible: false,
                    sortable: false,
                    width: 150
                }
            ];
        }
    },

    methods: {
        handleExport(selectedPartner) {
            let partnerCodeLabel = 'Partner code';
            let partnerNameLabel = 'Partner name';
            if (!!this.type) {
                if (this.type === 'customer') {
                    partnerCodeLabel = 'Customer code';
                    partnerNameLabel = 'Customer name';
                } else if (this.type === 'vendor') {
                    partnerCodeLabel = 'Vendor code';
                    partnerNameLabel = 'Vendor name';
                } else if (this.type === 'employee') {
                    partnerCodeLabel = 'Employee code';
                    partnerNameLabel = 'Employee name';
                }
            }

            let type = this.type;
            if (!!selectedPartner) {
                type = selectedPartner.type;
            }

            this.$program.dialog({
                component: 'accounting.reports.partner-ledger.export',
                params: {
                    type,
                    forcedPreview: false,
                    title: this.$t('Export'),
                    partnerId: (selectedPartner || {})._id,
                    columns: [
                        {field: 'issueDate', label: 'Issue Date', width: 15, selected: true},
                        {field: 'dueDate', label: 'Due date', width: 15, selected: true},
                        {field: 'documentNo', label: 'Document No', width: 20, selected: true},
                        {field: 'partner.code', label: partnerCodeLabel, width: 30, selected: true},
                        {field: 'partner.name', label: partnerNameLabel, width: 90, selected: true},
                        {field: 'partner.tinIdentity', label: 'TIN / Identity', width: 30, selected: false},
                        {field: 'account.code', label: 'Account code', width: 30, selected: false},
                        {field: 'account.name', label: 'Account name', width: 60, selected: false},
                        {field: 'description', label: 'Description', width: 90, selected: true},
                        {field: 'reference', label: 'Reference', width: 30, selected: false},
                        {field: 'branch.code', label: 'Branch code', width: 30, selected: false},
                        {field: 'branch.name', label: 'Branch name', width: 45, selected: false},
                        {field: 'financialProject.code', label: 'Project code', width: 30, selected: false},
                        {field: 'financialProject.name', label: 'Project name', width: 45, selected: false},
                        {field: 'currency.name', label: 'Currency', width: 20},
                        {field: 'currencyRate', label: 'Currency rate', width: 20},
                        {field: 'initialBalance', label: 'Initial Balance', width: 20, selected: true},
                        {field: 'debit', label: 'Debit', width: 20, selected: true},
                        {field: 'credit', label: 'Credit', width: 20, selected: true},
                        {field: 'balance', label: 'Balance', width: 20, selected: true},
                        {field: 'initialBalanceFC', label: 'Initial balance (FC)', width: 20},
                        {field: 'debitFC', label: 'Debit (FC)', width: 20},
                        {field: 'creditFC', label: 'Credit (FC)', width: 20},
                        {field: 'balanceFC', label: 'Balance (FC)', width: 20}
                    ]
                },
                onSubmit: async payload => {
                    this.$params('loading', true);

                    let result = null;

                    if (!!payload.groupByCurrency && !!selectedPartner) {
                        result = await this.$rpc('accounting.partner-ledger-export-grouped', {
                            ...payload,
                            type: this.type,
                            formatOptions: this.$store.getters['session/formatterOptions']
                        });
                    } else {
                        try {
                            result = await this.$rpc('accounting.partner-ledger-export', {
                                ...payload,
                                type: this.type,
                                formatOptions: this.$store.getters['session/formatterOptions']
                            });
                        } catch (error) {
                            this.$program.message('error', error.message);
                        }
                    }

                    if (!!result) {
                        if (payload.documentType === 'pdf') {
                            this.$program.dialog({
                                component: PrintPopup,
                                params: {title: result.title, url: result.url}
                            });
                        } else {
                            const link = document.createElement('a');
                            link.href = this.$app.absoluteUrl(`temp-files/${result.file}?filename=${result.name}`);
                            link.download = result.name;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);

                            this.$params('loading', false);
                        }
                    } else {
                        this.$params('loading', false);
                    }
                }
            });
        },
        async getPartners(query, params) {
            const user = this.$user;
            const partnerList = this.$refs.partnerList;
            let searchQuery = '';
            if (!!partnerList) {
                searchQuery =this.$params('searchQuery') || partnerList.searchQuery || '';
            }

            if (
                !user.isRoot &&
                !this.saleOrganizationSettings &&
                this.type === 'customer' &&
                user.partnerId &&
                this.$setting('sale.salesOrganizations')
            ) {
                const organization = await this.$collection('kernel.organizations').findOne({
                    scope: 'sale',
                    'team.partnerId': user.partnerId
                });

                if (_.isObject(organization)) {
                    const team = organization.team || [];
                    const currentMember = team.find(m => m.partnerId === user.partnerId);

                    this.saleOrganizationSettings = organization.settings || {};
                    if (
                        _.isObject(currentMember) &&
                        _.isObject(currentMember.settings) &&
                        !_.isEmpty(currentMember.settings)
                    ) {
                        this.saleOrganizationSettings = _.assign(this.saleOrganizationSettings, currentMember.settings);
                    }
                }
            }

            if (
                !user.isRoot &&
                !this.purchaseOrganizationSettings &&
                this.type === 'vendor' &&
                user.partnerId &&
                this.$setting('purchase.purchaseOrganizations')
            ) {
                const organization = await this.$collection('kernel.organizations').findOne({
                    scope: 'purchase',
                    'team.partnerId': user.partnerId
                });

                if (_.isObject(organization)) {
                    const team = organization.team || [];
                    const currentMember = team.find(m => m.partnerId === user.partnerId);

                    this.purchaseOrganizationSettings = organization.settings || {};
                    if (
                        _.isObject(currentMember) &&
                        _.isObject(currentMember.settings) &&
                        !_.isEmpty(currentMember.settings)
                    ) {
                        this.purchaseOrganizationSettings = _.assign(
                            this.purchaseOrganizationSettings,
                            currentMember.settings
                        );
                    }
                }
            }

            const result = await this.$rpc('accounting.partner-ledger-get-partners', {
                query: {
                    ...rawMongoQuery(query),
                    ...(!!this.type
                        ? {
                              'partner.type': this.type
                          }
                        : {
                              'partner.type': {$in: ['customer', 'vendor']}
                          })
                },
                partnerType: !!this.type ? this.type : null,
                ...(!!this.saleOrganizationSettings
                    ? {
                          partnerGroupIds: this.saleOrganizationSettings.customerGroupIds
                      }
                    : {}),
                ...(!!this.purchaseOrganizationSettings
                    ? {
                          partnerGroupIds: this.purchaseOrganizationSettings.vendorGroupIds
                      }
                    : {}),
                searchQuery,
                skip: query.$skip,
                limit: query.$limit
            });

            setTimeout(() => {
                if (result.data.length > 0 && !this.selectedPartner) {
                    this.$refs.partnerList.select(result.data[0]._id);
                }
            }, 50);

            return result;
        },
        getPartnersCellTemplate(item) {
            const type = (item || {}).type || 'customer';
            let avatarTypeClass = '';

            if (type === 'customer') {
                avatarTypeClass = 'is-customer';
            } else if (type === 'vendor') {
                avatarTypeClass = 'is-vendor';
            } else if (type === 'employee') {
                avatarTypeClass = 'is-employee';
            }

            item.balance = this.$app.roundNumber(item.balance, 2);

            const balance = this.$format(Math.abs(item.balance), 'currency');
            let balanceType = item.balance > 0 ? this.$t('Debit') : this.$t('Credit');
            let balanceTypeClass = item.balance > 0 ? 'is-debit' : 'is-credit';
            if (item.balance === 0) balanceType = '---';
            if (item.balance === 0) balanceTypeClass = '';
            return `
<div class="accounting-partner-ledger-partner-cell">
    <div class="partner-cell-avatar-container">
        <div class="partner-cell-avatar ${avatarTypeClass}">
            ${(item.name || '')
                .split(' ')
                .map(s => toUpper(s[0]))
                .slice(0, 3)
                .join('')}
        </div>
    </div>
    <div class="partner-cell-content">
        <div class="partner-cell-name">${item.name}</div>
        <div class="partner-cell-details">
            ${item.code} - ${
                item.type === 'customer'
                    ? this.$t('Customer')
                    : item.type === 'vendor'
                    ? this.$t('Vendor')
                    : this.$t('Employee')
            }
        </div>
    </div>
    <div class="partner-cell-balance-container" style="display:${item.balance === 0 ? 'none' : 'flex'};">
        <div class="partner-cell-balance ${balanceTypeClass}">${balance}</div>
        <div class="partner-cell-balance-type">${balanceType}</div>
    </div>
</div>
            `.trim();
        },
        async handleSyncBalances() {
            if (!this.selectedPartner || !this.selectedPartner._id) {
                return;
            }

            this.$params('loading', true);

            try {
                await this.$rpc('accounting.partner-balances-sync', {
                    partnerIds: [this.selectedPartner._id]
                });
            } catch (error) {
                this.$program.message('error', error.message);
            }

            this.$params('loading', false);
        },
        async handlePartnerSelect(selected) {
            this.$params('loading', true);

            const result = await this.$rpc('accounting.partner-ledger-get-partner-balance', {
                partnerId: selected[0]._id,
                scopeQuery: this.recordsScopeQuery
            });

            this.selectedPartner = {
                ...selected[0],
                ...result
            };

            this.isConnectedPartnerShown = false;

            this.initRecordsDebounced();

            this.$params('loading', false);
        },
        async handleToggleConnectedPartner() {
            this.$params('loading', true);

            const result = await this.$rpc('accounting.partner-ledger-get-partner-balance', {
                partnerId: this.selectedPartner._id,
                scopeQuery: this.recordsScopeQuery,
                includeConnectedPartners: !this.isConnectedPartnerShown
            });

            this.selectedPartner = {
                ...this.selectedPartner,
                ...result
            };

            this.isConnectedPartnerShown = !this.isConnectedPartnerShown;

            this.initRecordsDebounced();

            this.$params('loading', false);
        },
        async handleRecordsScopeChange(model) {
            this.recordsScopeQuery = model.query;

            const result = await this.$rpc('accounting.partner-ledger-get-partner-balance', {
                partnerId: this.selectedPartner._id,
                scopeQuery: this.recordsScopeQuery,
                includeConnectedPartners: !!this.isConnectedPartnerShown
            });

            this.selectedPartner = {
                ...this.selectedPartner,
                ...result
            };

            this.initRecordsDebounced();
        },
        async initRecords() {
            this.records = await this.$rpc('accounting.partner-ledger-get-records', {
                query: this.recordsFilters,
                partnerId: this.selectedPartner._id,
                includeConnectedPartners: this.isConnectedPartnerShown
            });
            console.log('Records loaded');
        }
    },
    async created() {
        this.$params('loading', true);

        this.initRecordsDebounced = _.debounce(this.initRecords, 75, {leading: false, trailing: true});

        this.currencies = await this.$collection('kernel.currencies').find({
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });

        this.initialized = true;

        this.$nextTick(() => {
            this.$params('loading', false);
        });
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.accounting-partner-ledger-records-container {
    position: relative;
    width: 100%;
    height: 100%;
    padding-top: 75px;
    overflow: hidden;

    .accounting-partner-ledger-records-header {
        position: absolute;
        display: flex;
        align-items: stretch;
        width: 100%;
        top: 0;
        left: 0;
        height: 75px;
        padding: 15px;
        background-color: #fff;
        border-bottom: 1px solid $border-color;

        .selected-partner-info {
            display: flex;
            flex: 1;
            min-width: 0;

            .selected-partner-avatar-container {
                display: flex;
                flex-flow: column nowrap;
                justify-content: center;
                align-items: center;
                width: 45px;
                height: 45px;

                .selected-partner-avatar {
                    display: flex;
                    flex-flow: column nowrap;
                    justify-content: center;
                    align-items: center;
                    width: 100%;
                    height: 100%;
                    border-radius: 50%;
                    background-color: $primary;
                    color: #fff;
                    font-size: 14px;
                    line-height: 1;

                    &.is-customer {
                        background-color: palate('orange', '500');
                    }

                    &.is-vendor {
                        background-color: palate('green', '500');
                    }

                    &.is-employee {
                        background-color: palate('purple', '500');
                    }
                }
            }

            .selected-partner-content {
                display: flex;
                flex-flow: column nowrap;
                justify-content: center;
                flex: 1;
                margin-left: 15px;
                line-height: 1;
                min-width: 0;

                .selected-partner-name {
                    width: 100%;
                    margin-bottom: 10px;
                    font-size: 16px;
                    font-weight: 700;
                    overflow: hidden;
                    @include text-truncate();
                }

                .selected-partner-details {
                    width: 100%;
                    font-size: 13px;
                    overflow: hidden;
                    @include text-truncate();
                }
            }
        }

        //.accounting-partner-ledger-search {
        //    display: flex;
        //    align-items: center;
        //    margin-left: 30px;
        //}

        .selected-partner-reports {
            display: flex;
            flex: 1 1 0;
            justify-content: flex-end;
            margin-right: 6.5px;

            .selected-partner-report {
                display: flex;
                flex-flow: column nowrap;
                justify-content: center;
                padding: 0 15px;
                border-right: 1px solid $border-color-light;
                line-height: 1;

                &:first-child {
                    padding-left: 0;
                }

                &:last-child {
                    padding-right: 0;
                    border-right: none;
                }

                .report-label {
                    margin-bottom: 6px;
                    font-size: 12px;
                    color: $text-color-lighter;
                }

                .report-value {
                    font-size: 18px;
                    white-space: nowrap;
                }
            }
        }
    }

    .accounting-partner-ledger-records-container {
        position: relative;
        width: 100%;
        height: 100%;
        padding-top: 45px;
        background-color: white;
        overflow: hidden;

        .records-top {
            position: absolute;
            display: flex;
            flex-flow: row nowrap;
            top: 0;
            left: 0;
            width: 100%;
            height: 45px;
            padding: 0;

            .records-top-actions {
                position: relative;
                padding: 9.5px 20px 9.5px 8.5px;

                &:after {
                    position: absolute;
                    top: 50%;
                    right: 0;
                    width: 1px;
                    height: 27px;
                    transform: translateY(-50%);
                    background-color: $border-color;
                    content: ' ';
                }
            }

            .ui-scope {
                flex: 1 1 0;
                margin-left: 10px;
            }

            .records-top-search {
                position: relative;
                padding: 9.5px 8.5px 9.5px 20px;
                flex: 0 0 240px;

                &:before {
                    position: absolute;
                    top: 50%;
                    left: 0;
                    width: 1px;
                    height: 27px;
                    transform: translateY(-50%);
                    background-color: $border-color;
                    content: ' ';
                }
            }
        }

        .records-content {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;

            .ui-table:not(.is-editable) {
                .ag-header-row.ag-header-row-column {
                    border-top: none;
                }

                .ag-cell {
                    display: flex;
                    flex-flow: row nowrap;
                    align-items: center;
                    font-size: 14px;
                    line-height: 1.4;
                    padding: 0 12px;

                    &.is-number-cell {
                        justify-content: flex-end;

                        .ui-table-relation-renderer {
                            justify-content: flex-end;
                        }

                        .relation-renderer-icon {
                            margin-right: 4px;
                        }

                        .relation-renderer-label {
                            flex-grow: initial;
                            flex-shrink: initial;
                            flex-basis: initial;
                        }
                    }

                    .ag-cell-wrapper.ag-row-group {
                        flex: 1 1 0;
                    }
                }
            }

            .ui-table:not(.is-editable) .ag-floating-bottom-viewport .ag-floating-bottom-container .ag-row,
            .ui-table:not(.is-editable) .ag-pinned-left-floating-bottom .ag-row,
            .ui-table:not(.is-editable) .ag-pinned-right-floating-bottom .ag-row {
                height: 100% !important;
            }

            .ui-table:not(.is-editable) .ag-floating-bottom-viewport .ag-floating-bottom-container .ag-row .ag-cell,
            .ui-table:not(.is-editable) .ag-pinned-left-floating-bottom .ag-row .ag-cell,
            .ui-table:not(.is-editable) .ag-pinned-right-floating-bottom .ag-row .ag-cell {
                height: 100% !important;
                line-height: 36px !important;
            }
        }
    }
}

.accounting-partner-ledger-empty-state {
    position: relative;
    background-color: #fff;
}

.accounting-partner-ledger-partner-cell {
    display: flex;
    padding: 10px;

    .partner-cell-avatar-container {
        display: flex;
        flex-flow: column nowrap;
        justify-content: center;
        align-items: center;
        width: 40px;
        height: 40px;
    }

    .partner-cell-avatar {
        display: flex;
        flex-flow: column nowrap;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        background-color: $primary;
        color: #fff;
        font-size: 14px;
        line-height: 1;

        &.is-customer {
            background-color: palate('orange', '500');
        }

        &.is-vendor {
            background-color: palate('green', '500');
        }

        &.is-employee {
            background-color: palate('purple', '500');
        }
    }

    .partner-cell-content {
        display: flex;
        flex-flow: column;
        justify-content: center;
        flex: 1;
        margin-left: 10px;
        min-width: 0;
    }

    .partner-cell-name {
        width: 100%;
        overflow: hidden;
        @include text-truncate();
        font-size: 14px;
        line-height: 1;
        margin-bottom: 8px;
    }

    .partner-cell-details {
        width: 100%;
        overflow: hidden;
        @include text-truncate();
        font-size: 12px;
        color: #7f878a;
        line-height: 1;
    }

    .partner-cell-balance-container {
        display: flex;
        flex-flow: column nowrap;
        align-items: flex-end;
        flex: 0 0 130px;
        padding-left: 10px;

        .partner-cell-balance {
            font-size: 14px;
            line-height: 1;
            margin-bottom: 8px;

            &.is-debit {
                color: $success;
            }

            &.is-credit {
                color: $danger;
            }
        }

        .partner-cell-balance-type {
            font-size: 12px;
            color: #7f878a;
            line-height: 1;
        }
    }
}
</style>
